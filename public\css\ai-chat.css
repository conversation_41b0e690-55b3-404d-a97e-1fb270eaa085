/* AI Chat Styles */
.ai-chat-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    font-family: 'Inter', sans-serif;
}

.ai-chat-button {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(to right, #9333ea, #6366f1);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

.ai-chat-button:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

.ai-chat-window {
    position: absolute;
    bottom: 80px;
    right: 0;
    width: 350px;
    height: 500px;
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transition: all 0.3s ease;
    opacity: 0;
    transform: translateY(20px) scale(0.95);
    pointer-events: none;
}

.ai-chat-window.active {
    opacity: 1;
    transform: translateY(0) scale(1);
    pointer-events: all;
}

.ai-chat-header {
    background: linear-gradient(to right, #9333ea, #6366f1);
    color: white;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ai-chat-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.ai-chat-header-actions {
    display: flex;
    gap: 10px;
}

.ai-chat-header-button {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.8;
    transition: opacity 0.2s;
}

.ai-chat-header-button:hover {
    opacity: 1;
}

.ai-chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 15px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.ai-chat-message {
    max-width: 80%;
    padding: 10px 15px;
    border-radius: 18px;
    line-height: 1.4;
    font-size: 14px;
    position: relative;
}

.ai-chat-message.user {
    background-color: #9333ea;
    color: white;
    align-self: flex-end;
    border-bottom-right-radius: 4px;
}

.ai-chat-message.ai {
    background-color: #f3f4f6;
    color: #1f2937;
    align-self: flex-start;
    border-bottom-left-radius: 4px;
}

.ai-chat-input-container {
    padding: 15px;
    border-top: 1px solid #e5e7eb;
    display: flex;
    gap: 10px;
}

.ai-chat-input {
    flex: 1;
    border: 1px solid #e5e7eb;
    border-radius: 20px;
    padding: 10px 15px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.2s;
}

.ai-chat-input:focus {
    border-color: #9333ea;
}

.ai-chat-send-button {
    background: linear-gradient(to right, #9333ea, #6366f1);
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
}

.ai-chat-send-button:hover {
    transform: scale(1.05);
}

.ai-chat-send-button:disabled {
    background: #e5e7eb;
    cursor: not-allowed;
}

.ai-chat-login-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    text-align: center;
    z-index: 10;
}

.ai-chat-login-message {
    margin-bottom: 20px;
    font-size: 16px;
    color: #4b5563;
}

.ai-chat-login-button {
    background: linear-gradient(to right, #9333ea, #6366f1);
    color: white;
    border: none;
    border-radius: 20px;
    padding: 10px 20px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
}

.ai-chat-login-button:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.ai-chat-typing-indicator {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 8px 12px;
    background-color: #f3f4f6;
    border-radius: 18px;
    align-self: flex-start;
    margin-top: 5px;
}

.ai-chat-typing-dot {
    width: 8px;
    height: 8px;
    background-color: #9333ea;
    border-radius: 50%;
    animation: typing-animation 1.4s infinite ease-in-out;
}

.ai-chat-typing-dot:nth-child(1) {
    animation-delay: 0s;
}

.ai-chat-typing-dot:nth-child(2) {
    animation-delay: 0.2s;
}

.ai-chat-typing-dot:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing-animation {
    0%, 60%, 100% {
        transform: translateY(0);
        opacity: 0.6;
    }
    30% {
        transform: translateY(-4px);
        opacity: 1;
    }
}
