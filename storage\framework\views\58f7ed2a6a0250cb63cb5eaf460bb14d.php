<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">

<head>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-QWR2LGRD93"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-QWR2LGRD93');
    </script>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <title><?php echo e(config('app.name', 'Digitora')); ?> - User Dashboard</title>
    <link rel="icon" type="image/x-icon" href="<?php echo e(asset('images/digitora-logo.png')); ?>">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Scripts -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>

    <!-- AI Chat CSS -->
    <link rel="stylesheet" href="<?php echo e(asset('css/ai-chat.css')); ?>">
    <link href="<?php echo e(asset('css/dashboard-user.css')); ?>" rel="stylesheet">

    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>

    
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
</head>

<body class="font-sans antialiased">
    <div class="flex min-h-screen bg-gray-50">
        <!-- Sidebar for desktop -->
        <aside class="hidden w-64 flex-col border-r bg-white lg:flex">
            <div class="flex h-16 items-center border-b px-4">
                <a href="<?php echo e(route('home')); ?>" class="flex items-center gap-2">
                    <div
                        class="flex h-8 w-8 items-center justify-center rounded-md bg-gradient-to-br from-indigo-600 to-purple-700 text-white">
                        <span class="text-lg font-bold">D</span>
                    </div>
                    <span class="text-xl font-bold">Digitora</span>
                </a>
            </div>
            <nav class="flex-1 overflow-auto py-4">
                <div class="px-3">
                    <h2 class="mb-2 px-4 text-xs font-semibold uppercase tracking-wider text-gray-500">Main</h2>
                    <div class="space-y-1">
                        <a href="<?php echo e(route('user.dashboard')); ?>"
                            class="flex items-center rounded-md px-4 py-2 text-sm font-medium <?php echo e(request()->routeIs('user.dashboard') ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg"
                                class="mr-3 h-5 w-5 <?php echo e(request()->routeIs('user.dashboard') ? 'text-indigo-500' : 'text-gray-400'); ?>"
                                fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                            </svg>
                            Dashboard
                        </a>
                        <a href="<?php echo e(route('user.purchases')); ?>"
                            class="flex items-center rounded-md px-4 py-2 text-sm font-medium <?php echo e(request()->routeIs('user.purchases') ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg"
                                class="mr-3 h-5 w-5 <?php echo e(request()->routeIs('user.purchases') ? 'text-indigo-500' : 'text-gray-400'); ?>"
                                fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                            </svg>
                            My Purchases
                        </a>
                        <a href="<?php echo e(route('cart.index')); ?>"
                            class="flex items-center rounded-md px-4 py-2 text-sm font-medium <?php echo e(request()->routeIs('cart.*') ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg"
                                class="mr-3 h-5 w-5 <?php echo e(request()->routeIs('cart.*') ? 'text-indigo-500' : 'text-gray-400'); ?>"
                                fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                            Shopping Cart
                        </a>
                    </div>
                </div>

                <div class="mt-8 px-3">
                    <h2 class="mb-2 px-4 text-xs font-semibold uppercase tracking-wider text-gray-500">Account</h2>
                    <div class="space-y-1">
                        <a href="<?php echo e(route('user.profile')); ?>"
                            class="flex items-center rounded-md px-4 py-2 text-sm font-medium <?php echo e(request()->routeIs('user.profile') ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg"
                                class="mr-3 h-5 w-5 <?php echo e(request()->routeIs('user.profile') ? 'text-indigo-500' : 'text-gray-400'); ?>"
                                fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                            Profile
                        </a>
                        <a href="<?php echo e(route('user.settings')); ?>"
                            class="flex items-center rounded-md px-4 py-2 text-sm font-medium <?php echo e(request()->routeIs('user.settings') ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg"
                                class="mr-3 h-5 w-5 <?php echo e(request()->routeIs('user.settings') ? 'text-indigo-500' : 'text-gray-400'); ?>"
                                fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            Settings
                        </a>
                    </div>
                </div>

                <div class="mt-8 px-3">
                    <h2 class="mb-2 px-4 text-xs font-semibold uppercase tracking-wider text-gray-500">Browse</h2>
                    <div class="space-y-1">
                        <a href="<?php echo e(route('user.browse')); ?>"
                            class="flex items-center rounded-md px-4 py-2 text-sm font-medium <?php echo e(request()->routeIs('user.browse') ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg"
                                class="mr-3 h-5 w-5 <?php echo e(request()->routeIs('user.browse') ? 'text-indigo-500' : 'text-gray-400'); ?>"
                                fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                            </svg>
                            Browse Products
                        </a>
                        <a href="<?php echo e(route('user.browse-stores')); ?>"
                            class="flex items-center rounded-md px-4 py-2 text-sm font-medium <?php echo e(request()->routeIs('user.browse-stores') ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg"
                                class="mr-3 h-5 w-5 <?php echo e(request()->routeIs('user.browse-stores') ? 'text-indigo-500' : 'text-gray-400'); ?>"
                                fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                            </svg>
                            Browse Stores
                        </a>
                    </div>
                </div>

                <div class="mt-8 px-3">
                    <h2 class="mb-2 px-4 text-xs font-semibold uppercase tracking-wider text-gray-500">Seller</h2>
                    <div class="space-y-1">
                        <a href="<?php echo e(route('user.seller-dashboard')); ?>"
                            class="flex items-center rounded-md px-4 py-2 text-sm font-medium <?php echo e(request()->routeIs('user.seller-dashboard') ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg"
                                class="mr-3 h-5 w-5 <?php echo e(request()->routeIs('user.seller-dashboard') ? 'text-indigo-500' : 'text-gray-400'); ?>"
                                fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                            <?php if(Auth::check() && Auth::user()->is_seller): ?>
                                Seller Dashboard
                            <?php else: ?>
                                Become a Seller
                            <?php endif; ?>
                        </a>
                    </div>
                </div>
            </nav>
            <div class="border-t p-4">
                <div class="flex items-center gap-3">
                    <?php if(auth()->guard()->check()): ?>
                        <div class="flex h-10 w-10 items-center justify-center rounded-full bg-indigo-600 text-white">
                            <?php echo e(substr(Auth::user()->name, 0, 1)); ?>

                        </div>
                        <div>
                            <p class="text-sm font-medium"><?php echo e(Auth::user()->name); ?></p>
                            <p class="text-xs text-gray-500">User Account</p>
                        </div>
                    <?php else: ?>
                        <div class="flex h-10 w-10 items-center justify-center rounded-full bg-gray-200 text-gray-600">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm font-medium">Guest</p>
                            <p class="text-xs text-gray-500">Not logged in</p>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="mt-4">
                    <?php if(auth()->guard()->check()): ?>
                        <form method="POST" action="<?php echo e(route('logout')); ?>">
                            <?php echo csrf_field(); ?>
                            <button type="submit"
                                class="flex w-full items-center rounded-md px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-gray-900">
                                <svg xmlns="http://www.w3.org/2000/svg" class="mr-3 h-5 w-5 text-gray-400" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                                </svg>
                                Logout
                            </button>
                        </form>
                    <?php else: ?>
                        <div class="space-y-2">
                            <a href="<?php echo e(route('login')); ?>"
                                class="flex w-full items-center rounded-md px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-gray-900">
                                <svg xmlns="http://www.w3.org/2000/svg" class="mr-3 h-5 w-5 text-gray-400" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                                </svg>
                                Login
                            </a>
                            <a href="<?php echo e(route('register')); ?>"
                                class="flex w-full items-center rounded-md px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-gray-900">
                                <svg xmlns="http://www.w3.org/2000/svg" class="mr-3 h-5 w-5 text-gray-400" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                                </svg>
                                Register
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </aside>

        <!-- Main content -->
        <div class="flex flex-1 flex-col">
            <!-- Header -->
            <header class="flex h-16 items-center justify-between border-b bg-white px-4 sm:px-6">
                <div class="flex items-center">
                    <button x-data @click="$dispatch('toggle-sidebar')"
                        class="mr-2 rounded-md p-2 text-gray-500 hover:bg-gray-100 hover:text-gray-600 lg:hidden">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round" class="h-6 w-6">
                            <line x1="4" x2="20" y1="12" y2="12"></line>
                            <line x1="4" x2="20" y1="6" y2="6"></line>
                            <line x1="4" x2="20" y1="18" y2="18"></line>
                        </svg>
                        <span class="sr-only">Toggle Menu</span>
                    </button>
                    <div class="lg:hidden">
                        <a href="<?php echo e(route('home')); ?>" class="flex items-center gap-2">
                            <div
                                class="flex h-8 w-8 items-center justify-center rounded-md bg-gradient-to-br from-indigo-600 to-purple-700 text-white">
                                <span class="text-lg font-bold">D</span>
                            </div>
                            <span class="text-xl font-bold">Digitora</span>
                        </a>
                    </div>
                </div>
                <div class="flex items-center gap-3">
                    <a href="<?php echo e(route('cart.index')); ?>"
                        class="relative rounded-full p-2 text-gray-500 hover:bg-gray-100 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                        <span class="sr-only">View cart</span>
                        <?php if(isset($cartItemCount) && $cartItemCount > 0): ?>
                            <span
                                class="absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-indigo-600 rounded-full"><?php echo e($cartItemCount); ?></span>
                        <?php endif; ?>
                    </a>
                    <?php if(auth()->guard()->check()): ?>
                        <div x-data="{ open: false }" class="relative">
                            <button @click="open = !open"
                                class="flex items-center gap-2 rounded-full text-gray-500 hover:text-gray-600">
                                <div
                                    class="flex h-8 w-8 items-center justify-center rounded-full bg-indigo-600 text-white">
                                    <?php echo e(substr(Auth::user()->name, 0, 1)); ?>

                                </div>
                                <span class="sr-only">Open user menu</span>
                            </button>
                            <div x-show="open" @click.away="open = false"
                                class="absolute right-0 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
                                role="menu" aria-orientation="vertical" aria-labelledby="user-menu-button"
                                tabindex="-1">
                                <a href="<?php echo e(route('user.dashboard')); ?>"
                                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                    role="menuitem">Dashboard</a>
                                <a href="<?php echo e(route('user.profile')); ?>"
                                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                    role="menuitem">Profile</a>
                                <a href="<?php echo e(route('user.settings')); ?>"
                                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                    role="menuitem">Settings</a>
                                <form method="POST" action="<?php echo e(route('logout')); ?>">
                                    <?php echo csrf_field(); ?>
                                    <button type="submit"
                                        class="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                                        role="menuitem">Logout</button>
                                </form>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="flex items-center space-x-3">
                            <a href="<?php echo e(route('login')); ?>"
                                class="text-sm font-medium text-indigo-600 hover:text-indigo-500">Login</a>
                            <a href="<?php echo e(route('register')); ?>"
                                class="rounded-md bg-indigo-600 px-3 py-1.5 text-sm font-medium text-white hover:bg-indigo-500">Register</a>
                        </div>
                    <?php endif; ?>
                </div>
            </header>

            <!-- Mobile sidebar -->
            <div x-data="{ open: false }" @toggle-sidebar.window="open = !open" x-show="open"
                class="fixed inset-0 z-50 lg:hidden" x-cloak>
                <div x-show="open" @click="open = false" class="fixed inset-0 bg-gray-600 bg-opacity-75"></div>
                <div class="fixed inset-y-0 left-0 flex w-64 flex-col bg-white">
                    <div class="flex h-16 items-center justify-between border-b px-4">
                        <a href="<?php echo e(route('home')); ?>" class="flex items-center gap-2">
                            <div
                                class="flex h-8 w-8 items-center justify-center rounded-md bg-gradient-to-br from-indigo-600 to-purple-700 text-white">
                                <span class="text-lg font-bold">D</span>
                            </div>
                            <span class="text-xl font-bold">Digitora</span>
                        </a>
                        <button @click="open = false"
                            class="rounded-md p-2 text-gray-500 hover:bg-gray-100 hover:text-gray-600">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6">
                                <line x1="18" y1="6" x2="6" y2="18"></line>
                                <line x1="6" y1="6" x2="18" y2="18"></line>
                            </svg>
                            <span class="sr-only">Close Menu</span>
                        </button>
                    </div>
                    <nav class="flex-1 overflow-auto py-4">
                        <div class="px-3">
                            <h2 class="mb-2 px-4 text-xs font-semibold uppercase tracking-wider text-gray-500">Main
                            </h2>
                            <div class="space-y-1">
                                <a href="<?php echo e(route('user.dashboard')); ?>"
                                    class="flex items-center rounded-md px-4 py-2 text-sm font-medium <?php echo e(request()->routeIs('user.dashboard') ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'); ?>">
                                    <svg xmlns="http://www.w3.org/2000/svg"
                                        class="mr-3 h-5 w-5 <?php echo e(request()->routeIs('user.dashboard') ? 'text-indigo-500' : 'text-gray-400'); ?>"
                                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                                    </svg>
                                    Dashboard
                                </a>
                                <a href="<?php echo e(route('user.purchases')); ?>"
                                    class="flex items-center rounded-md px-4 py-2 text-sm font-medium <?php echo e(request()->routeIs('user.purchases') ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'); ?>">
                                    <svg xmlns="http://www.w3.org/2000/svg"
                                        class="mr-3 h-5 w-5 <?php echo e(request()->routeIs('user.purchases') ? 'text-indigo-500' : 'text-gray-400'); ?>"
                                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                                    </svg>
                                    My Purchases
                                </a>
                                <a href="<?php echo e(route('cart.index')); ?>"
                                    class="flex items-center rounded-md px-4 py-2 text-sm font-medium <?php echo e(request()->routeIs('cart.*') ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'); ?>">
                                    <svg xmlns="http://www.w3.org/2000/svg"
                                        class="mr-3 h-5 w-5 <?php echo e(request()->routeIs('cart.*') ? 'text-indigo-500' : 'text-gray-400'); ?>"
                                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                                    </svg>
                                    Shopping Cart
                                </a>
                            </div>
                        </div>

                        <div class="mt-8 px-3">
                            <h2 class="mb-2 px-4 text-xs font-semibold uppercase tracking-wider text-gray-500">Account
                            </h2>
                            <div class="space-y-1">
                                <a href="<?php echo e(route('user.profile')); ?>"
                                    class="flex items-center rounded-md px-4 py-2 text-sm font-medium <?php echo e(request()->routeIs('user.profile') ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'); ?>">
                                    <svg xmlns="http://www.w3.org/2000/svg"
                                        class="mr-3 h-5 w-5 <?php echo e(request()->routeIs('user.profile') ? 'text-indigo-500' : 'text-gray-400'); ?>"
                                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                    Profile
                                </a>
                                <a href="<?php echo e(route('user.settings')); ?>"
                                    class="flex items-center rounded-md px-4 py-2 text-sm font-medium <?php echo e(request()->routeIs('user.settings') ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'); ?>">
                                    <svg xmlns="http://www.w3.org/2000/svg"
                                        class="mr-3 h-5 w-5 <?php echo e(request()->routeIs('user.settings') ? 'text-indigo-500' : 'text-gray-400'); ?>"
                                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    </svg>
                                    Settings
                                </a>
                            </div>
                        </div>

                        <div class="mt-8 px-3">
                            <h2 class="mb-2 px-4 text-xs font-semibold uppercase tracking-wider text-gray-500">Browse
                            </h2>
                            <div class="space-y-1">
                                <a href="<?php echo e(route('user.browse')); ?>"
                                    class="flex items-center rounded-md px-4 py-2 text-sm font-medium <?php echo e(request()->routeIs('user.browse') ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'); ?>">
                                    <svg xmlns="http://www.w3.org/2000/svg"
                                        class="mr-3 h-5 w-5 <?php echo e(request()->routeIs('user.browse') ? 'text-indigo-500' : 'text-gray-400'); ?>"
                                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                                    </svg>
                                    Browse Products
                                </a>
                                <a href="<?php echo e(route('user.browse-stores')); ?>"
                                    class="flex items-center rounded-md px-4 py-2 text-sm font-medium <?php echo e(request()->routeIs('user.browse-stores') ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'); ?>">
                                    <svg xmlns="http://www.w3.org/2000/svg"
                                        class="mr-3 h-5 w-5 <?php echo e(request()->routeIs('user.browse-stores') ? 'text-indigo-500' : 'text-gray-400'); ?>"
                                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                                    </svg>
                                    Browse Stores
                                </a>
                            </div>
                        </div>

                        <div class="mt-8 px-3">
                            <h2 class="mb-2 px-4 text-xs font-semibold uppercase tracking-wider text-gray-500">Seller
                            </h2>
                            <div class="space-y-1">
                                <a href="<?php echo e(route('user.seller-dashboard')); ?>"
                                    class="flex items-center rounded-md px-4 py-2 text-sm font-medium <?php echo e(request()->routeIs('user.seller-dashboard') ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'); ?>">
                                    <svg xmlns="http://www.w3.org/2000/svg"
                                        class="mr-3 h-5 w-5 <?php echo e(request()->routeIs('user.seller-dashboard') ? 'text-indigo-500' : 'text-gray-400'); ?>"
                                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M13 10V3L4 14h7v7l9-11h-7z" />
                                    </svg>
                                    <?php if(Auth::check() && Auth::user()->is_seller): ?>
                                        Seller Dashboard
                                    <?php else: ?>
                                        Become a Seller
                                    <?php endif; ?>
                                </a>
                            </div>
                        </div>
                    </nav>
                    <div class="border-t p-4">
                        <div class="flex items-center gap-3">
                            <?php if(auth()->guard()->check()): ?>
                                <div
                                    class="flex h-10 w-10 items-center justify-center rounded-full bg-indigo-600 text-white">
                                    <?php echo e(substr(Auth::user()->name, 0, 1)); ?>

                                </div>
                                <div>
                                    <p class="text-sm font-medium"><?php echo e(Auth::user()->name); ?></p>
                                    <p class="text-xs text-gray-500">User Account</p>
                                </div>
                            <?php else: ?>
                                <div
                                    class="flex h-10 w-10 items-center justify-center rounded-full bg-gray-200 text-gray-600">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-sm font-medium">Guest</p>
                                    <p class="text-xs text-gray-500">Not logged in</p>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="mt-4">
                            <?php if(auth()->guard()->check()): ?>
                                <form method="POST" action="<?php echo e(route('logout')); ?>">
                                    <?php echo csrf_field(); ?>
                                    <button type="submit"
                                        class="flex w-full items-center rounded-md px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-gray-900">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="mr-3 h-5 w-5 text-gray-400"
                                            fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                                        </svg>
                                        Logout
                                    </button>
                                </form>
                            <?php else: ?>
                                <div class="space-y-2">
                                    <a href="<?php echo e(route('login')); ?>"
                                        class="flex w-full items-center rounded-md px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-gray-900">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="mr-3 h-5 w-5 text-gray-400"
                                            fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                                        </svg>
                                        Login
                                    </a>
                                    <a href="<?php echo e(route('register')); ?>"
                                        class="flex w-full items-center rounded-md px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-gray-900">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="mr-3 h-5 w-5 text-gray-400"
                                            fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                                        </svg>
                                        Register
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Page content -->
            <main class="flex-1 overflow-auto p-4 sm:p-6">
                <?php if(session('success')): ?>
                    <div class="mb-4 rounded-md bg-green-50 p-4 text-sm text-green-700">
                        <?php echo e(session('success')); ?>

                    </div>
                <?php endif; ?>

                <?php if(session('error')): ?>
                    <div class="mb-4 rounded-md bg-red-50 p-4 text-sm text-red-700">
                        <?php echo e(session('error')); ?>

                    </div>
                <?php endif; ?>

                <?php echo $__env->yieldContent('content'); ?>
            </main>
        </div>
    </div>

    <!-- AI Chat Component -->
    <?php echo $__env->make('components.ai-chat', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <!-- AI Chat JS -->
    <script src="<?php echo e(asset(js_path() . '/ai-chat.js')); ?>" defer></script>
</body>

</html>
<?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\digitora\resources\views/layouts/user-dashboard.blade.php ENDPATH**/ ?>