<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddAdditionalFieldsToUsersTable extends Migration
{
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('phone')->nullable()->after('email');
            $table->text('bio')->nullable()->after('phone');
            $table->string('avatar')->nullable()->default('avatars/default-avatar.png')->after('bio');
            $table->boolean('notification_order')->default(true)->after('avatar');
            $table->boolean('notification_payment')->default(true)->after('notification_order');
            $table->boolean('notification_product')->default(true)->after('notification_payment');
            $table->boolean('notification_marketing')->default(false)->after('notification_product');
        });
    }

    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'phone',
                'bio',
                'avatar',
                'notification_order',
                'notification_payment',
                'notification_product',
                'notification_marketing',
            ]);
        });
    }
}