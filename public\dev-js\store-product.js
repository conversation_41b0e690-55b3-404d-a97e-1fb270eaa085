/**
 * Store product page functionality
 */
document.addEventListener('DOMContentLoaded', function() {
    // Product image gallery functionality
    const mainImage = document.getElementById('main-image');
    const thumbnailContainers = document.querySelectorAll('.thumbnail-container');

    // Add click event to each thumbnail
    thumbnailContainers.forEach(container => {
        container.addEventListener('click', function() {
            // Get the image source from the thumbnail
            const thumbnailImg = this.querySelector('.thumbnail');
            const imgSrc = thumbnailImg.getAttribute('data-src');

            // Update the main image
            mainImage.src = imgSrc;

            // Remove border from all thumbnails
            thumbnailContainers.forEach(item => {
                item.classList.remove('border-2', 'border-indigo-500');
                item.classList.add('hover:opacity-80');
            });

            // Add border to the clicked thumbnail
            this.classList.add('border-2', 'border-indigo-500');
            this.classList.remove('hover:opacity-80');
        });
    });

    // Handle related product "Add to Cart" forms
    const relatedProductForms = document.querySelectorAll('.related-product-form');
    relatedProductForms.forEach(form => {
        form.addEventListener('submit', function(event) {
            event.preventDefault();

            const productId = this.querySelector('input[name="product_id"]').value;
            const csrfToken = document.getElementById('csrf_token').value;

            fetch(this.action, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRF-TOKEN': csrfToken
                },
                body: new URLSearchParams({
                    'product_id': productId,
                    'quantity': 1,
                    '_token': csrfToken
                })
            })
            .then(response => {
                if (response.ok) {
                    // Show success message
                    Swal.fire({
                        title: "Success!",
                        text: "Product added to cart successfully!",
                        icon: "success",
                        timer: 2000,
                        showConfirmButton: false
                    });

                    // Optionally, update cart count in the UI
                    // This depends on how your cart count is displayed
                } else {
                    throw new Error('Failed to add product to cart');
                }
            })
            .catch(error => {
                Swal.fire({
                    title: "Error!",
                    text: "Failed to add product to cart. Please try again.",
                    icon: "error"
                });
            });
        });
    });

    // Buy form submission
    const buyForm = document.getElementById('buy_form');
    if (buyForm) {
        buyForm.addEventListener('submit', function(event) {
            event.preventDefault();

            Swal.fire({
                title: "Loading",
                text: "Mohon tunggu sebentar",
                timerProgressBar: true,
                didOpen: () => {
                    Swal.showLoading();
                },
            });

            const productId = this.querySelector("input[name='product_id']").value;
            const csrfToken = document.getElementById('csrf_token').value;

            // Check transaction
            fetch('/product/check-transaction', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken
                },
                body: JSON.stringify({
                    productId: productId
                })
            })
            .then(response => response.json())
            .then(data => {
                Swal.close();
                if (data.status === 'new-order') {
                    // Proceed with new transaction
                    getSnapToken(productId);
                } else if (data.status === 'pending') {
                    // There's a pending transaction
                    Swal.fire({
                        title: "Transaksi tertunda",
                        text: data.message,
                        icon: "warning",
                        showCancelButton: true,
                        confirmButtonText: "Buat transaksi baru",
                        cancelButtonText: "Lanjut transaksi sebelumnya",
                        reverseButtons: true,
                    }).then((result) => {
                        if (result.isConfirmed) {
                            // Cancel old transaction and create new one
                            Swal.fire({
                                title: "Loading",
                                text: "Mohon tunggu sebentar",
                                timerProgressBar: true,
                                didOpen: () => {
                                    Swal.showLoading();
                                },
                            });

                            fetch('/product/cancel-transaction', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'X-CSRF-TOKEN': csrfToken
                                },
                                body: JSON.stringify({
                                    orderId: data.orderId
                                })
                            })
                            .then(response => response.json())
                            .then(cancelData => {
                                Swal.close();
                                getSnapToken(productId);
                            })
                            .catch(error => {
                                Swal.close();
                                Swal.fire("Gagal", "Gagal membatalkan transaksi lama.", "error");
                            });
                        } else {
                            // Continue with previous transaction
                            snap.pay(data.snap_token, {
                                onSuccess: function(result) {
                                    window.location.href = `${appUrl}/user/purchases`;
                                },
                                onPending: function(result) {
                                    window.location.href = `${appUrl}/user/purchases`;
                                },
                                onError: function(result) {
                                    location.reload();
                                },
                            });
                        }
                    });
                } else if (data.status === 'success') {
                    // Transaction is already completed
                    Swal.fire("Transaksi Selesai", data.message, "info");
                } else {
                    Swal.fire("Gagal", data.message ?? "Terjadi kesalahan.", "error");
                }
            })
            .catch(error => {
                Swal.close();
                Swal.fire("Oops!", "Gagal memeriksa transaksi. Coba lagi nanti.", "error");
            });
        });
    }
});

// Get Snap token for payment
function getSnapToken(productId) {
    const csrfToken = document.getElementById('csrf_token').value;
    const appUrl = window.location.origin;

    fetch('/product/buy', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': csrfToken
        },
        body: JSON.stringify({
            productId: productId
        })
    })
    .then(response => response.json())
    .then(data => {
        snap.pay(data.snap_token, {
            onSuccess: function(result) {
                window.location.href = `${appUrl}/user/purchases`;
            },
            onPending: function(result) {
                window.location.href = `${appUrl}/user/purchases`;
            },
            onError: function(result) {
                location.reload();
            },
            onClose: function() {
                Swal.fire({
                    title: "Pembayaran dibatalkan",
                    text: "Anda dapat melanjutkan pembayaran nanti melalui halaman pembelian",
                    icon: "warning"
                });
            }
        });
    })
    .catch(error => {
        Swal.fire("Oops!", "Gagal memproses pembayaran. Coba lagi nanti.", "error");
    });
}
