<?php

namespace App\Http\Controllers;

use App\Models\Plan;
use App\Models\Course;
use App\Models\Invoice;
use Illuminate\Support\Str;
use App\Models\Subscription;
use Illuminate\Http\Request;
use App\Models\ProgressDetail;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\DataTables\InvoiceDataTable;
use App\Models\Revenue;
use App\Models\Exam;
use App\Models\ExamAccess;
use App\Models\Order;
use App\Models\Product;
use Midtrans\Transaction;


class OrderController extends Controller
{
    public function checkTransaction(Request $request)
    {
        $product = Product::find($request->productId);
        if (!$product)
            return response()->json(['message' => 'Produk tidak ditemukan!'], 404);

        $order = Order::where('buyer_id', Auth::id())
            ->where('product_id', '=', $request->productId)->first();

        if (!$order)
            return response()->json(["status" => "new-order"], 200);

        $successOrder = Order::where('buyer_id', Auth::id())
            ->where('product_id', '=', $request->productId)->where('status', '=', 'success')->first();

        if ($successOrder)
            return response()->json(['message' => 'Anda sudah melakukan pembayaran pada produk ' . $product->name, "status" => "success"], 200);
        else {
            $pendingOrder = Order::where('buyer_id', Auth::id())
                ->where('product_id', '=', $request->productId)->where('status', '=', 'pending')->first();
            if ($pendingOrder)
                return response()->json(['message' => 'Anda mempunyai pembayaran dengan status pending! Silakan melanjutkan pembayaran sebelumnya! Klik tombol buat transaksi baru untuk menghapus transaksi sebelumnya!', "status" => "pending", "orderId" => $pendingOrder->order_id, "snap_token" => $pendingOrder->snap_token], 200);
            else
                return response()->json(["status" => "new-order"], 200);
        }
    }

    public function cancelTransaction(Request $request)
    {
        \Midtrans\Config::$serverKey = config('services.midtrans.serverKey');
        \Midtrans\Config::$isProduction = config('services.midtrans.isProduction');
        \Midtrans\Config::$isSanitized = config('services.midtrans.isSanitized');
        \Midtrans\Config::$is3ds = config('services.midtrans.is3ds');

        try {
            $response = Transaction::cancel($request->orderId);
        } catch (\Throwable $th) {
            Order::where('order_id', '=', $request->orderId)->update([
                'status' => 'canceled'
            ]);
        }
    }

    public function store(Request $request)
    {
        // dapatin data midtrans
        \Midtrans\Config::$serverKey = config('services.midtrans.serverKey');
        \Midtrans\Config::$isProduction = config('services.midtrans.isProduction');
        \Midtrans\Config::$isSanitized = config('services.midtrans.isSanitized');
        \Midtrans\Config::$is3ds = config('services.midtrans.is3ds');

        // /course/{course:id}/referral/{kode_referral}
        // get kode referral


        // bikin transaksi untuk create invoice
        DB::transaction(function () use ($request) {
            $buyer = Auth::user();

            $product = Product::find($request->productId);

            $amount = $product->price; // jika tidak ada diskon, maka harga normal

            $discountPrice = $product->discount_price;

            if ($discountPrice != null && $discountPrice != 0)
                $amount = (int) ($discountPrice); // jika ada diskon, maka harga diskon

            // bikin invoice
            $order = Order::create([
                'order_id' => Str::random(20) . time(),
                'buyer_id' => $buyer->id,
                'product_id' => $product->id,
                'amount' => floatval($amount),
                'status' => 'pending',
            ]);

            // payload untuk midtrans
            $payload = [
                'transaction_details' => [
                    'order_id' => $order->order_id,
                    'gross_amount' => $order->amount,
                ],
                'customer_details' => [
                    'first_name' => $buyer->name,
                    'email' => $buyer->email,
                ],
                'item_details' => [
                    [
                        'id' => $order->order_id,
                        'price' => $product->price,
                        'quantity' => 1,
                        'name' => $product->name,
                    ]
                ]
            ];

            // channel pembayaran

            // jika ada diskon, tambahkan item diskon
            if ($discountPrice != null && $discountPrice != 0) {
                array_push($payload["item_details"], array(
                    'id' => 'a1',
                    'price' => -(int) ($product->price - $discountPrice),
                    'quantity' => 1,
                    'name' => 'Discount'
                ));
            }

            // buat snap token, dan simpan invoice
            $snapToken = \Midtrans\Snap::getSnapToken($payload);
            $order->status = 'pending';
            $order->payment_status = 'pending';

            $order->snap_token = $snapToken;
            $order->save();

            $this->response['snap_token'] = $snapToken;
        });


        return response()->json($this->response);
    }

    public function notification(Request $request)
    {
        \Midtrans\Config::$serverKey = config('services.midtrans.serverKey');
        \Midtrans\Config::$isProduction = config('services.midtrans.isProduction');
        \Midtrans\Config::$isSanitized = config('services.midtrans.isSanitized');
        \Midtrans\Config::$is3ds = config('services.midtrans.is3ds');

        $notif = new \Midtrans\Notification();

        DB::transaction(function () use ($notif) {

            // capture data dari midtrans
            $transaction = $notif->transaction_status;
            $type = $notif->payment_type;
            $orderId = $notif->order_id;
            $fraud = $notif->fraud_status;

            // ambil data invoice yang dibayar
            $orders = Order::where('order_id', '=', $orderId)->get();

            if ($orders === null) {
                // Handle the case where no matching invoice was found
                return;
            }

            Order::where('order_id', $orderId)->update([
                'payment_status' => $transaction,
                'payment_method' => $type,
            ]);

            // tentukan aksi berdasarkan status transaksi
            if ($transaction == 'capture') {
                if ($type == 'credit_card') {

                    if ($fraud == 'challenge') {
                        Order::where('order_id', $orderId)->update(['status' => 'pending']);
                    } else {
                        Order::where('order_id', $orderId)->update(['status' => 'success']);
                    }
                }
            } elseif ($transaction == 'settlement') {
                // pembayaran berhasil
                Order::where('order_id', $orderId)->update(['status' => 'success']);
            } elseif ($transaction == 'pending') {
                Order::where('order_id', $orderId)->update(['status' => 'pending']);
            } elseif ($transaction == 'deny') {
                Order::where('order_id', $orderId)->update(['status' => 'failed']);
            } elseif ($transaction == 'expire') {
                Order::where('order_id', $orderId)->update(['status' => 'expired']);
            } elseif ($transaction == 'cancel') {
                Order::where('order_id', $orderId)->update(['status' => 'canceled']);
            }
        });

        return;
    }
}
