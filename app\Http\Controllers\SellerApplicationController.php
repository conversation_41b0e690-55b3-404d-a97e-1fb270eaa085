<?php

namespace App\Http\Controllers;

use App\Models\SellerApplication;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class SellerApplicationController extends Controller
{
    public function __construct()
    {
        $this->middleware('throttle:50,60')->only('store');
    }

    public function create(Request $request)
    {
        $user = Auth::user();
        $existingApplication = SellerApplication::where('user_id', $user->id)->first();

        if ($existingApplication) {
            if ($existingApplication->status === 'pending') {
                return redirect()->route('seller.pending');
            } elseif ($existingApplication->status === 'approved') {
                return redirect()->route('seller.dashboard');
            } elseif ($existingApplication->status === 'rejected') {
                return redirect()->route('seller.rejected');
            }
        }

        $currentStep = $request->session()->get('seller_step', 1);
        $formData = $request->session()->get('seller_form_data', []);

        // Auto-fill email, full_name, and phone from authenticated user
        if (!isset($formData['email'])) {
            $formData['email'] = $user->email;
        }

        if (!isset($formData['full_name'])) {
            $formData['full_name'] = $user->name;
        }

        if (!isset($formData['phone']) && !empty($user->phone)) {
            $formData['phone'] = $user->phone;
        }

        $request->session()->put('seller_form_data', $formData);

        return view('seller.apply', [
            'currentStep' => $currentStep,
            'formData' => $formData,
            'user' => $user,
        ]);
    }

    public function store(Request $request)
    {
        $user = Auth::user();
        $existingApplication = SellerApplication::where('user_id', $user->id)->first();

        if ($existingApplication) {
            if ($existingApplication->status === 'pending') {
                return redirect()->route('seller.pending');
            } elseif ($existingApplication->status === 'approved') {
                return redirect()->route('seller.dashboard');
            } elseif ($existingApplication->status === 'rejected') {
                return redirect()->route('seller.rejected');
            }
        }

        $step = (int) $request->input('step', 1);
        $currentStep = $request->session()->get('seller_step', 1);
        $isAjaxRequest = $request->ajax() || $request->has('_ajax_request');

        Log::info("Step received: {$step}, Current step: {$currentStep}, Ajax: " . ($isAjaxRequest ? 'true' : 'false'));

        if ($step < $currentStep) {
            $request->session()->put('seller_step', $step);

            if ($isAjaxRequest) {
                return view('seller.apply', [
                    'currentStep' => $step,
                    'formData' => $request->session()->get('seller_form_data', []),
                    'ajaxRequest' => true
                ]);
            }

            return redirect()->route('seller.apply');
        }

        $formData = $request->session()->get('seller_form_data', []);
        $inputData = $request->except('_token', 'step', 'id_document', 'store_logo');
        foreach ($inputData as $key => $value) {
            if (is_string($value)) {
                $inputData[$key] = strip_tags($value);
            }
        }
        $formData = array_merge($formData, $inputData);

        $tempFiles = $request->session()->get('seller_temp_files', []);
        $userId = Auth::id();

        try {
            if ($request->hasFile('id_document') && $currentStep === 2) {
                $file = $request->file('id_document');
                $rules = [
                    'id_document' => 'nullable|file|mimes:jpg,jpeg,png,pdf|max:2048',
                ];
                $messages = [
                    'id_document.mimes' => 'The ID document must be a JPG, PNG, or PDF file.',
                    'id_document.max' => 'The ID document must be less than 2MB.',
                ];
                $request->validate($rules, $messages);

                // Store in a user-specific folder
                $tempPath = $file->store(
                    "private/temp/id_documents/{$userId}",
                    'local'
                );
                $tempFiles['id_document'] = $tempPath;
                $formData['id_document_path'] = $tempPath;
            }

            if ($request->hasFile('store_logo') && $currentStep === 4) {
                $file = $request->file('store_logo');
                $rules = [
                    'store_logo' => 'nullable|file|mimes:jpg,jpeg,png|max:2048',
                ];
                $messages = [
                    'store_logo.mimes' => 'The store logo must be a JPG or PNG file.',
                    'store_logo.max' => 'The store logo must be less than 2MB.',
                ];
                $request->validate($rules, $messages);

                // Store in a user-specific folder
                $tempPath = $file->store(
                    "private/temp/store_logos/{$userId}",
                    'local'
                );
                $tempFiles['store_logo'] = $tempPath;
                $formData['store_logo_path'] = $tempPath;
            }
        } catch (\Exception $e) {
            Log::error("File upload error: {$e->getMessage()}");
            return back()->withErrors(['file_upload' => 'Error uploading file: ' . $e->getMessage()])->withInput();
        }

        $request->session()->put('seller_form_data', $formData);
        $request->session()->put('seller_temp_files', $tempFiles);

        $this->validateStep($request, $currentStep);

        $request->session()->put('seller_step', $step);

        if ($step < 5) {
            return redirect()->route('seller.apply');
        }

        Log::info('Proceeding with final submission');

        $mergedData = array_merge($formData, $request->all());
        Log::info('Merged data for final validation: ', $mergedData);

        try {
            $validator = \Illuminate\Support\Facades\Validator::make($mergedData, [
                'phone' => 'required|string|max:20|regex:/^[+]?[0-9\s-]+$/',
                'id_type' => 'required|in:passport,national_id,drivers_license',
                'id_number' => 'required|string|max:255',
                'bank_name' => 'required|string|max:255',
                'account_number' => 'required|string|max:255',
                'account_holder_name' => 'required|string|max:255',
                'store_name' => 'required|string|max:50',
                'store_description' => 'required|string|max:500',
            ]);

            if ($validator->fails()) {
                Log::error("Final validation failed: " . json_encode($validator->errors()));
                $request->session()->put('seller_step', 4);
                return redirect()->route('seller.apply')
                    ->withErrors($validator)
                    ->withInput($mergedData);
            }
        } catch (\Exception $e) {
            Log::error("Validation error: {$e->getMessage()}");
            return back()->withErrors(['submission_error' => 'Validation error: ' . $e->getMessage()])->withInput();
        }

        $data = $formData;
        $data['user_id'] = $userId;

        // Generate a unique store slug
        $baseSlug = Str::slug($formData['store_name']);
        $slug = $baseSlug;
        $counter = 1;

        // Check if the slug already exists in the database
        while (SellerApplication::where('store_name_slug', $slug)->exists()) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        $data['store_name_slug'] = $slug;
        $data['status'] = 'approved';

        // Remove email and full_name from data as they're no longer stored in seller_applications
        unset($data['email']);
        unset($data['full_name']);

        try {
            $tempFiles = $request->session()->get('seller_temp_files', []);

            if (isset($tempFiles['id_document'])) {
                $tempPath = $tempFiles['id_document'];
                $finalPath = "private/id_documents/{$userId}/" . basename($tempPath);
                Storage::disk('local')->move($tempPath, $finalPath);
                $data['id_document'] = $finalPath;
            }

            if (isset($tempFiles['store_logo'])) {
                $tempPath = $tempFiles['store_logo'];
                $finalPath = "private/store_logos/{$userId}/" . basename($tempPath);
                Storage::disk('local')->move($tempPath, $finalPath);
                $data['store_logo'] = $finalPath;
            }

            unset($data['id_document_path']);
            unset($data['store_logo_path']);
        } catch (\Exception $e) {
            Log::error("File processing error: {$e->getMessage()}");
            return back()->withErrors(['file_upload' => 'Error processing files: ' . $e->getMessage()])->withInput();
        }

        try {
            $application = SellerApplication::create($data);

            $application->user->update([
                'is_seller' => 1,
            ]);

            Log::info('Seller application created successfully by user ID: ' . $userId);

            Storage::disk('local')->deleteDirectory('private/temp');

            $request->session()->forget(['seller_step', 'seller_form_data', 'seller_temp_files']);

            if ($request->ajax() || $request->has('_ajax_request')) {
                return response()->json([
                    'success' => true,
                    'redirect' => route('seller.success')
                ]);
            }

            return redirect()->route('seller.success');
        } catch (\Exception $e) {
            Log::error("Error creating seller application: {$e->getMessage()}");

            if ($request->ajax() || $request->has('_ajax_request')) {
                return response()->json([
                    'success' => false,
                    'error' => 'There was an error submitting your application: ' . $e->getMessage()
                ], 422);
            }

            return back()->withErrors(['submission_error' => 'There was an error submitting your application: ' . $e->getMessage()])->withInput();
        }
    }

    public function success()
    {
        $user = Auth::user();
        $application = SellerApplication::where('user_id', $user->id)->first();

        if (!$application) {
            return redirect()->route('seller.apply')->with('error', 'No application found. Please submit a new application.');
        }

        if ($application->status === 'pending') {
            return redirect()->route('seller.pending');
        } elseif ($application->status === 'approved') {
            return redirect()->route('seller.dashboard');
        } elseif ($application->status === 'rejected') {
            return redirect()->route('seller.rejected');
        }

        return view('seller.success');
    }

    public function serveFile($applicationId, $type)
    {
        $application = SellerApplication::findOrFail($applicationId);

        if (Auth::id() !== $application->user_id && !Auth::user()->is_admin) {
            abort(403, 'Unauthorized access to this file.');
        }

        $path = $type === 'id_document' ? $application->id_document : $application->store_logo;

        if (!$path || !Storage::disk('local')->exists($path)) {
            abort(404, 'File not found.');
        }

        Log::info("Serving file {$type} for application ID {$applicationId} to user ID " . Auth::id());

        return response()->file(storage_path('app/' . $path));
    }

    public function reapply(Request $request)
    {
        $user = Auth::user();
        $existingApplication = SellerApplication::where('user_id', $user->id)->first();

        if (!$existingApplication || $existingApplication->status !== 'rejected') {
            return redirect()->route('seller.apply');
        }

        // Delete the existing application and associated files
        if ($existingApplication->id_document) {
            Storage::disk('local')->delete($existingApplication->id_document);
        }
        if ($existingApplication->store_logo) {
            Storage::disk('local')->delete($existingApplication->store_logo);
        }
        $existingApplication->delete();

        Log::info("User ID {$user->id} is reapplying for seller status after rejection.");

        // Clear session data to start fresh
        $request->session()->forget(['seller_step', 'seller_form_data', 'seller_temp_files']);

        return redirect()->route('seller.apply')->with('success', 'You can now reapply as a seller.');
    }

    protected function validateStep(Request $request, int $step)
    {
        $rules = [];

        if ($step === 1) {
            $rules = [
                'full_name' => 'required|string|max:255',
                'phone' => 'required|string|max:20|regex:/^[+]?[0-9\s-]+$/',
            ];
            // Email is auto-filled and read-only, so no validation needed
        } elseif ($step === 2) {
            $rules = [
                'id_type' => 'required|in:passport,national_id,drivers_license',
                'id_number' => 'required|string|max:255',
            ];
        } elseif ($step === 3) {
            $rules = [
                'bank_name' => 'required|string|max:255',
                'account_number' => 'required|string|max:255',
                'account_holder_name' => 'required|string|max:255',
            ];
        } elseif ($step === 4) {
            $rules = [
                'store_name' => 'required|string|max:50',
                'store_description' => 'required|string|max:500',
            ];

            // Add custom validation to check if the store name slug is unique
            $validator = \Illuminate\Support\Facades\Validator::make($request->all(), $rules);

            $validator->after(function ($validator) use ($request) {
                $storeNameSlug = Str::slug($request->store_name);

                // Check if the slug already exists in approved applications
                $existingApplication = SellerApplication::where('store_name_slug', $storeNameSlug)
                    ->where('status', 'approved')
                    ->exists();

                if ($existingApplication) {
                    $validator->errors()->add(
                        'store_name',
                        'This store name is already taken. Please choose a different name.'
                    );
                }
            });

            if ($validator->fails()) {
                throw new \Illuminate\Validation\ValidationException($validator);
            }

            return;
        }

        if (!empty($rules)) {
            $request->validate($rules);
        }

        // If step 1 is being validated, update the user's name and phone
        if ($step === 1 && ($request->has('full_name') || $request->has('phone'))) {
            $user = Auth::user();
            $updateData = [];

            // Check if name needs to be updated
            if ($request->has('full_name') && $user->name !== $request->full_name) {
                $updateData['name'] = $request->full_name;
                // Update the session user object
                $user->name = $request->full_name;
            }

            // Check if phone needs to be updated
            if ($request->has('phone') && $user->phone !== $request->phone) {
                $updateData['phone'] = $request->phone;
                // Update the session user object
                $user->phone = $request->phone;
            }

            // Only perform the update if there are changes
            if (!empty($updateData)) {
                // Update user data in the database
                \Illuminate\Support\Facades\DB::table('users')
                    ->where('id', $user->id)
                    ->update($updateData);

                Log::info("Updated user ID {$user->id} profile data: " . json_encode($updateData));
            }
        }
    }

    public function index()
    {
        if (!Auth::user()->is_admin) {
            abort(403, 'Unauthorized access.');
        }

        $applications = SellerApplication::with('user')->get();
        return view('admin.seller_applications', compact('applications'));
    }

    public function updateStatus(Request $request, SellerApplication $application)
    {
        if (!Auth::user()->is_admin) {
            abort(403, 'Unauthorized access.');
        }

        $request->validate([
            'status' => 'required|in:approved,rejected',
        ]);

        // Check if the store slug is unique before approving
        if ($request->status === 'approved') {
            $storeSlug = $application->store_name_slug;
            $existingApplication = SellerApplication::where('store_name_slug', $storeSlug)
                ->where('id', '!=', $application->id)
                ->where('status', 'approved')
                ->exists();

            if ($existingApplication) {
                // Generate a new unique slug
                $baseSlug = $storeSlug;
                $counter = 1;

                while (SellerApplication::where('store_name_slug', $storeSlug)
                    ->where('id', '!=', $application->id)
                    ->where('status', 'approved')
                    ->exists()
                ) {
                    $storeSlug = $baseSlug . '-' . $counter;
                    $counter++;
                }

                // Update the application with the new unique slug
                $application->update([
                    'store_name_slug' => $storeSlug,
                ]);

                Log::info("Updated store slug for application ID {$application->id} to {$storeSlug} to ensure uniqueness");
            }
        }

        $application->update([
            'status' => $request->status,
        ]);

        // Only set is_seller to 1 if the application is approved
        if ($request->status === 'approved') {
            $application->user->update([
                'is_seller' => 1,
            ]);

            Log::info("User ID {$application->user_id} is now a seller with store slug: {$application->store_name_slug}");
        } else {
            // If rejected, ensure is_seller is set to 0
            $application->user->update([
                'is_seller' => 0,
            ]);
        }

        Log::info("Seller application ID {$application->id} status updated to {$request->status} by admin ID " . Auth::id());

        return redirect()->route('admin.seller_applications')->with('success', 'Application status updated successfully.');
    }
}
