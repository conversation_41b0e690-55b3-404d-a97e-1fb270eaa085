<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-QWR2LGRD93"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-QWR2LGRD93');
    </script>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ $seller->store_name }} - Digitora</title>
    <link rel="icon" type="image/x-icon" href="{{ asset('images/digitora-logo.png') }}">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Styles -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    {{-- SWEET ALERT 2 --}}
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- Scripts -->
    {{-- <script src="{{ asset('js/app.js') }}" defer></script> --}}
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>

    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>

    <style>
        /* Custom styles for improved design */
        :root {
            --primary-gradient: linear-gradient(135deg, #6366f1 0%, #a855f7 100%);
            --secondary-gradient: linear-gradient(135deg, #4f46e5 0%, #9333ea 100%);
            --primary-color: #6366f1;
            --secondary-color: #a855f7;
            --transition-speed: 0.3s;
        }

        body {
            background-color: #f9fafb;
        }

        /* Header and Navigation Styles */
        .hero-gradient {
            background: var(--primary-gradient);
            box-shadow: 0 4px 15px -3px rgba(99, 102, 241, 0.15);
        }

        .nav-link {
            position: relative;
            transition: all var(--transition-speed) ease;
            padding-bottom: 4px;
            margin-bottom: -4px;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            background: var(--primary-gradient);
            transition: width var(--transition-speed) ease;
            border-radius: 2px;
        }

        .nav-link:hover::after {
            width: 70%;
        }

        .nav-link.active::after {
            width: 70%;
        }

        .nav-link:hover {
            transform: translateY(-2px);
        }

        /* Card Animations */
        .card-hover {
            transition: transform var(--transition-speed) ease,
                box-shadow var(--transition-speed) ease,
                border-color var(--transition-speed) ease;
            border: 1px solid transparent;
        }

        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px -10px rgba(0, 0, 0, 0.1), 0 10px 20px -10px rgba(0, 0, 0, 0.04);
            border-color: rgba(99, 102, 241, 0.1);
        }

        /* Scroll Animations */
        .animate-on-scroll {
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.7s ease, transform 0.7s ease;
        }

        .animate-on-scroll.visible {
            opacity: 1;
            transform: translateY(0);
        }

        /* Cart Dropdown Styles */
        .cart-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            width: 320px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
            opacity: 0;
            transform: translateY(10px);
            visibility: hidden;
            transition: opacity var(--transition-speed) ease, transform var(--transition-speed) ease, visibility var(--transition-speed) ease;
            z-index: 50;
            overflow: hidden;
            border: 1px solid rgba(99, 102, 241, 0.1);
        }

        .cart-container:hover .cart-dropdown {
            opacity: 1;
            transform: translateY(0);
            visibility: visible;
        }

        /* Mobile Menu Enhancements */
        .mobile-menu-container {
            transition: max-height 0.5s ease;
            max-height: 0;
            overflow: hidden;
        }

        .mobile-menu-container.open {
            max-height: 500px;
        }

        .mobile-menu-item {
            transform: translateX(-10px);
            opacity: 0;
            transition: transform 0.3s ease, opacity 0.3s ease;
            transition-delay: calc(var(--index) * 0.05s);
        }

        .mobile-menu-container.open .mobile-menu-item {
            transform: translateX(0);
            opacity: 1;
        }

        /* Button Styles */
        .btn {
            transition: all var(--transition-speed) ease;
            position: relative;
            overflow: hidden;
        }

        .btn::after {
            content: '';
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: -100%;
            background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0) 100%);
            transition: left 0.6s ease;
        }

        .btn:hover::after {
            left: 100%;
        }

        /* Footer Enhancements */
        .footer-link {
            transition: all var(--transition-speed) ease;
            position: relative;
            display: inline-block;
        }

        .footer-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 1px;
            bottom: 0;
            left: 0;
            background-color: var(--primary-color);
            transition: width var(--transition-speed) ease;
        }

        .footer-link:hover::after {
            width: 100%;
        }

        .social-icon {
            transition: all var(--transition-speed) ease;
        }

        .social-icon:hover {
            transform: translateY(-3px);
            color: var(--primary-color);
        }
    </style>
</head>

<body class="font-sans antialiased bg-gray-50">
    <!-- Store Header -->
    <header class="bg-white shadow-sm sticky top-0 z-50">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between h-16">
                <!-- Store Logo and Name -->
                <div class="flex items-center">
                    <a href="{{ route('store.show', $seller->store_slug) }}" class="flex items-center gap-2">
                        @if ($seller->store_logo)
                            <img src="{{ route('store.logo', $seller->store_slug) }}" alt="{{ $seller->store_name }}"
                                class="h-9 w-9 rounded-lg object-cover shadow-sm">
                        @else
                            <div
                                class="flex h-9 w-9 items-center justify-center rounded-lg hero-gradient text-white shadow-sm">
                                <span class="text-base font-bold">{{ substr($seller->store_name, 0, 1) }}</span>
                            </div>
                        @endif
                        <span class="text-base font-bold text-gray-900">{{ $seller->store_name }}</span>
                    </a>

                    <!-- Edit Store Button (Only visible to store owner) -->
                    @auth
                        @if (Auth::user()->is_seller && Auth::user()->id === $seller->id)
                            <a href="{{ route('seller.settings') }}" class="ml-3 inline-flex items-center rounded-md bg-indigo-50 px-2.5 py-1 text-xs font-medium text-indigo-700 hover:bg-indigo-100 transition-colors duration-200">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                </svg>
                                Edit Store
                            </a>
                        @endif
                    @endauth
                </div>

                <!-- Navigation -->
                <nav class="hidden md:flex space-x-6">
                    <a href="{{ route('home') }}"
                        class="text-gray-700 hover:text-indigo-600 px-2 py-1 text-sm font-medium">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline-block mr-1" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                        </svg>
                        Home
                    </a>
                    <a href="{{ route('store.show', $seller->store_slug) }}"
                        class="text-gray-700 hover:text-indigo-600 px-2 py-1 text-sm font-medium {{ request()->routeIs('store.show') && !request()->route('category') ? 'text-indigo-600' : '' }}">
                        Store
                    </a>
                    <a href="{{ route('store.show', $seller->store_slug . '#products') }}"
                        class="text-gray-700 hover:text-indigo-600 px-2 py-1 text-sm font-medium {{ request()->route('category') ? 'text-indigo-600' : '' }}">
                        Products
                    </a>
                    <a href="{{ route('store.about', $seller->store_slug) }}"
                        class="text-gray-700 hover:text-indigo-600 px-2 py-1 text-sm font-medium {{ request()->routeIs('store.about') ? 'text-indigo-600' : '' }}">
                        About
                    </a>
                </nav>

                @php
                    // Define cart count variable
                    $cartCount = 0;
                    if (Auth::check()) {
                        $cart = \App\Models\Cart::where('user_id', Auth::id())->first();
                        if ($cart) {
                            $cartCount = $cart->items->sum('quantity');
                        }
                    } else {
                        $sessionId = session()->get('cart_session_id');
                        if ($sessionId) {
                            $cart = \App\Models\Cart::where('session_id', $sessionId)->first();
                            if ($cart) {
                                $cartCount = $cart->items->sum('quantity');
                            }
                        }
                    }
                @endphp

                <!-- Search and Cart -->
                <div class="hidden md:flex items-center space-x-4">
                    <form action="{{ route('store.search', $seller->store_slug) }}" method="GET" class="relative">
                        <input type="text" name="query" placeholder="Search..." required
                            class="w-48 pl-8 pr-3 py-1.5 rounded-lg border border-gray-300 focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 text-sm">
                        <div class="absolute inset-y-0 left-0 pl-2.5 flex items-center pointer-events-none">
                            <svg class="h-4 w-4 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                                fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd"
                                    d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                                    clip-rule="evenodd" />
                            </svg>
                        </div>
                    </form>
                    <a href="{{ route('cart.index') }}"
                        class="relative text-gray-700 hover:text-purple-600 transition-colors duration-200 p-1.5"
                        aria-label="View Cart">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                        @if ($cartCount > 0)
                            <span
                                class="absolute -top-1 -right-1 bg-purple-600 text-white rounded-full w-4 h-4 flex items-center justify-center text-xs">{{ $cartCount }}</span>
                        @endif
                    </a>
                </div>

                <!-- Mobile menu button -->
                <div class="flex items-center md:hidden space-x-3">
                    <a href="{{ route('cart.index') }}"
                        class="relative text-gray-700 hover:text-purple-600 transition-colors duration-200 p-1"
                        aria-label="View Cart">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                        @if ($cartCount > 0)
                            <span
                                class="absolute -top-1 -right-1 bg-purple-600 text-white rounded-full w-4 h-4 flex items-center justify-center text-xs">{{ $cartCount }}</span>
                        @endif
                    </a>
                    <button type="button"
                        class="text-gray-500 hover:text-indigo-600 focus:outline-none transition-colors duration-300"
                        x-data="{}"
                        @click="document.getElementById('mobile-menu').classList.toggle('hidden')">
                        <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile menu, show/hide based on menu state. -->
        <div class="md:hidden hidden" id="mobile-menu">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3">
                <a href="{{ route('home') }}"
                    class="flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-300 text-gray-700 hover:bg-gray-50 hover:text-indigo-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                    </svg>
                    Main Site
                </a>
                <a href="{{ route('cart.index') }}"
                    class="flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-300 text-gray-700 hover:bg-gray-50 hover:text-indigo-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    Cart
                    @if ($cartCount > 0)
                        <span
                            class="ml-2 bg-purple-600 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs">{{ $cartCount }}</span>
                    @endif
                </a>
                <a href="{{ route('store.show', $seller->store_slug) }}"
                    class="block px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-300 {{ request()->routeIs('store.show') && !request()->route('category') ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-gray-50 hover:text-indigo-600' }}">
                    Store
                </a>
                <a href="{{ route('store.show', $seller->store_slug . '#products') }}"
                    class="block px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-300 {{ request()->route('category') ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-gray-50 hover:text-indigo-600' }}">
                    Products
                </a>
                <a href="{{ route('store.about', $seller->store_slug) }}"
                    class="block px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-300 {{ request()->routeIs('store.about') ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-gray-50 hover:text-indigo-600' }}">
                    About
                </a>

                <!-- Edit Store Button (Only visible to store owner) - Mobile -->
                @auth
                    @if (Auth::user()->is_seller && Auth::user()->id === $seller->id)
                        <a href="{{ route('seller.settings') }}"
                            class="flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-300 text-indigo-600 bg-indigo-50 hover:bg-indigo-100">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                            Edit Store
                        </a>
                    @endif
                @endauth
            </div>
            <div class="pt-2 pb-3 border-t border-gray-200">
                <div class="px-4 py-2">
                    <form action="{{ route('store.search', $seller->store_slug) }}" method="GET" class="relative">
                        <input type="text" name="query" placeholder="Search products..." required
                            class="w-full pl-9 pr-3 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-4 w-4 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                                fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd"
                                    d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                                    clip-rule="evenodd" />
                            </svg>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main>
        @yield('content')
    </main>

    <!-- Store Footer -->
    <footer class="bg-white border-t mt-8 py-8">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div>
                    <div class="flex items-center gap-2 group">
                        @if ($seller->store_logo)
                            <img src="{{ route('store.logo', $seller->store_slug) }}"
                                alt="{{ $seller->store_name }}" class="h-10 w-10 rounded-lg object-cover shadow-sm">
                        @else
                            <div
                                class="flex h-10 w-10 items-center justify-center rounded-lg hero-gradient text-white shadow-md">
                                <span class="text-lg font-bold">{{ substr($seller->store_name, 0, 1) }}</span>
                            </div>
                        @endif
                        <span class="text-lg font-bold text-gray-900">{{ $seller->store_name }}</span>
                    </div>
                    <p class="mt-3 text-sm text-gray-600 leading-relaxed">
                        {{ Str::limit($seller->store_description ?? 'Welcome to our digital products store. We offer high-quality digital products designed to help you succeed.', 120) }}
                    </p>
                </div>
                <div>
                    <h3 class="text-sm font-semibold text-gray-900 tracking-wider uppercase mb-3">Quick Links</h3>
                    <ul class="space-y-2">
                        <li>
                            <a href="{{ route('home') }}"
                                class="text-sm text-gray-600 hover:text-indigo-600 transition-colors duration-300 flex items-center">
                                {{-- <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                </svg> --}}
                                Main Site
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('store.show', $seller->store_slug) }}"
                                class="text-sm text-gray-600 hover:text-indigo-600 transition-colors duration-300 flex items-center">
                                {{-- <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                </svg> --}}
                                Store Home
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('store.show', $seller->store_slug . '#products') }}"
                                class="text-sm text-gray-600 hover:text-indigo-600 transition-colors duration-300 flex items-center">
                                {{-- <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                </svg> --}}
                                Products
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('store.about', $seller->store_slug) }}"
                                class="text-sm text-gray-600 hover:text-indigo-600 transition-colors duration-300 flex items-center">
                                {{-- <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                </svg> --}}
                                About Us
                            </a>
                        </li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4">Contact</h3>
                    <ul class="space-y-3">
                        @if ($seller->email)
                            <li class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-indigo-500 mr-3"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                    <path stroke-linecap="round" stroke-linejoin="round"
                                        d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                </svg>
                                <span class="text-gray-600">{{ $seller->email }}</span>
                            </li>
                        @endif
                        @if ($seller->phone)
                            <li class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-indigo-500 mr-3"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                    <path stroke-linecap="round" stroke-linejoin="round"
                                        d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                </svg>
                                <span class="text-gray-600">{{ $seller->phone }}</span>
                            </li>
                        @endif
                    </ul>

                    <!-- Social Media Links (Commented out as requested) -->
                    {{-- <div class="mt-6 flex space-x-6">
                        <a href="#" class="text-gray-400 hover:text-indigo-500 transition-colors duration-300">
                            <span class="sr-only">Facebook</span>
                            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path fill-rule="evenodd"
                                    d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                                    clip-rule="evenodd" />
                            </svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-indigo-500 transition-colors duration-300">
                            <span class="sr-only">Instagram</span>
                            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path fill-rule="evenodd"
                                    d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z"
                                    clip-rule="evenodd" />
                            </svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-indigo-500 transition-colors duration-300">
                            <span class="sr-only">Twitter</span>
                            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path
                                    d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                            </svg>
                        </a>
                    </div> --}}
                </div>
            </div>
            {{-- <div class="mt-10 border-t border-gray-200 pt-8 md:flex md:items-center md:justify-between">
                <div class="flex space-x-6 md:order-2">
                    <a href="#" class="text-gray-500 hover:text-indigo-600 transition-colors duration-300">
                        <span class="text-sm">Terms of Service</span>
                    </a>
                    <a href="#" class="text-gray-500 hover:text-indigo-600 transition-colors duration-300">
                        <span class="text-sm">Privacy Policy</span>
                    </a>
                </div>
                <p class="mt-8 text-base text-gray-500 md:mt-0 md:order-1">
                    &copy; {{ date('Y') }} {{ $seller->store_name }}. All rights reserved.
                </p>
            </div> --}}
        </div>
    </footer>

    <!-- Animation Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Animate elements on scroll
            const animateElements = document.querySelectorAll('.animate-on-scroll');

            function checkScroll() {
                animateElements.forEach(element => {
                    const elementTop = element.getBoundingClientRect().top;
                    const windowHeight = window.innerHeight;

                    if (elementTop < windowHeight * 0.9) {
                        element.classList.add('visible');
                    }
                });
            }

            // Initial check
            checkScroll();

            // Check on scroll
            window.addEventListener('scroll', checkScroll);
        });
    </script>
</body>

</html>
