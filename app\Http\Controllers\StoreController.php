<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Order;
use App\Models\Product;
use App\Models\SellerApplication;
use App\Models\ProductCategory;
use App\Models\ProductSubcategory;
use App\Models\ProductDetailedCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class StoreController extends Controller
{
    /**
     * Display the seller's store front.
     *
     * @param  string  $storeNameSlug
     * @return \Illuminate\View\View
     */
    public function show($storeNameSlug)
    {
        // Find the seller by store name using the sellerApplication relationship
        $seller = User::where('is_seller', true)
            ->whereHas('sellerApplication', function ($query) use ($storeNameSlug) {
                $query->where('store_name_slug', $storeNameSlug)
                    ->where('status', 'approved');
            })
            ->with('sellerApplication')
            ->firstOrFail();

        // Add a computed store_slug property to the seller
        $seller->store_slug = $storeNameSlug;

        $products = Product::where('seller_id', $seller->id)
            ->where('status', 'active')
            ->paginate(10);

        // Get product categories for filtering
        // First try to get categories from the new structure
        $categoriesQuery = Product::where('seller_id', $seller->id)
            ->where('status', 'active')
            ->whereNotNull('category_id');

        $hasNewCategories = $categoriesQuery->exists();

        if ($hasNewCategories) {
            // Use the new category structure
            $categoryData = Product::where('seller_id', $seller->id)
                ->where('status', 'active')
                ->whereNotNull('category_id')
                ->with(['productCategory', 'productSubcategory', 'productDetailedCategory'])
                ->get();

            // Group by category and get counts
            $categories = [];
            $categoryProductCounts = [];

            foreach ($categoryData as $product) {
                // Use subcategory name if available, otherwise fallback to category
                $categoryName = $product->productSubcategory ?
                    $product->productSubcategory->name : ($product->productCategory ? $product->productCategory->name : null);

                if ($categoryName && $product->subcategory_id) {
                    $subCat = ProductSubcategory::find($product->subcategory_id);
                    if ($subCat) {
                        // Use slug as the key for better SEO-friendly URLs
                        $subCatSlug = $subCat->slug;
                        if (!isset($categories[$subCatSlug])) {
                            $categories[$subCatSlug] = $categoryName;
                            $categoryProductCounts[$subCatSlug] = 1;
                        } else {
                            $categoryProductCounts[$subCatSlug]++;
                        }
                    }
                }
            }
        } else {
            // Fallback to legacy category field
            $categories = Product::where('seller_id', $seller->id)
                ->where('status', 'active')
                ->select('category')
                ->distinct()
                ->get()
                ->pluck('category');

            // Get product count per category
            $categoryProductCounts = [];
            foreach ($categories as $category) {
                $categoryProductCounts[$category] = Product::where('seller_id', $seller->id)
                    ->where('status', 'active')
                    ->where('category', $category)
                    ->count();
            }
        }

        return view('store.show', compact('seller', 'products', 'categories', 'categoryProductCounts', 'hasNewCategories'));
    }

    /**
     * Display all of the seller's products with filtering options.
     *
     * @param  string  $storeNameSlug
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function allProducts($storeNameSlug, Request $request)
    {
        // Find the seller by store name using the sellerApplication relationship
        $seller = User::where('is_seller', true)
            ->whereHas('sellerApplication', function ($query) use ($storeNameSlug) {
                $query->where('store_name_slug', $storeNameSlug)
                    ->where('status', 'approved');
            })
            ->with('sellerApplication')
            ->firstOrFail();

        // Add a computed store_slug property to the seller
        $seller->store_slug = $storeNameSlug;

        // Start building the query
        $query = Product::where('seller_id', $seller->id)
            ->where('status', 'active');

        // Apply price range filter
        $priceRange = $request->input('price_range');
        if ($priceRange && $priceRange != 'all') {
            if (strpos($priceRange, '-') !== false) {
                list($min, $max) = explode('-', $priceRange);
                $query->whereBetween('price', [$min, $max]);
            } elseif (strpos($priceRange, '+') !== false) {
                $min = str_replace('+', '', $priceRange);
                $query->where('price', '>=', $min);
            }
        }

        // Apply rating filter
        $rating = $request->input('rating');
        if ($rating && $rating != 'all') {
            $query->where('average_rating', '>=', $rating);
        }

        // Apply sorting
        $sort = $request->input('sort', 'newest');
        switch ($sort) {
            case 'price_low':
                $query->orderBy('price', 'asc');
                break;
            case 'price_high':
                $query->orderBy('price', 'desc');
                break;
            case 'popular':
                $query->orderBy('average_rating', 'desc');
                break;
            case 'newest':
            default:
                $query->orderBy('created_at', 'desc');
                break;
        }

        // Apply pagination - always use 10 items per page
        $products = $query->paginate(10)->withQueryString();

        // Get all categories for the sidebar
        // First try to get categories from the new structure
        $categoriesQuery = Product::where('seller_id', $seller->id)
            ->where('status', 'active')
            ->whereNotNull('category_id');

        $hasNewCategories = $categoriesQuery->exists();

        if ($hasNewCategories) {
            // Use the new category structure
            $categoryData = Product::where('seller_id', $seller->id)
                ->where('status', 'active')
                ->whereNotNull('category_id')
                ->with(['productCategory', 'productSubcategory', 'productDetailedCategory'])
                ->get();

            // Group by category and get counts
            $categories = [];
            $categoryProductCounts = [];

            foreach ($categoryData as $product) {
                // Use subcategory name if available, otherwise fallback to category
                $categoryName = $product->productSubcategory ?
                    $product->productSubcategory->name : ($product->productCategory ? $product->productCategory->name : null);

                if ($categoryName) {
                    if (!isset($categories[$product->subcategory_id])) {
                        $categories[$product->subcategory_id] = $categoryName;
                        $categoryProductCounts[$product->subcategory_id] = 1;
                    } else {
                        $categoryProductCounts[$product->subcategory_id]++;
                    }
                }
            }
        } else {
            // Fallback to legacy category field
            $categories = Product::where('seller_id', $seller->id)
                ->where('status', 'active')
                ->select('category')
                ->distinct()
                ->get()
                ->pluck('category');

            // Get product count per category
            $categoryProductCounts = [];
            foreach ($categories as $cat) {
                $categoryProductCounts[$cat] = Product::where('seller_id', $seller->id)
                    ->where('status', 'active')
                    ->where('category', $cat)
                    ->count();
            }
        }

        // Set category to 'all' for the view
        $category = 'all';

        return view('store.category', compact('seller', 'products', 'categories', 'category', 'categoryProductCounts', 'hasNewCategories'));
    }

    /**
     * Display the seller's products filtered by category.
     *
     * @param  string  $storeNameSlug
     * @param  string  $category
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function category($storeNameSlug, $categorySlug, Request $request)
    {
        // Find the seller by store name using the sellerApplication relationship
        $seller = User::where('is_seller', true)
            ->whereHas('sellerApplication', function ($query) use ($storeNameSlug) {
                $query->where('store_name_slug', $storeNameSlug)
                    ->where('status', 'approved');
            })
            ->with('sellerApplication')
            ->firstOrFail();

        // Add a computed store_slug property to the seller
        $seller->store_slug = $storeNameSlug;

        // Check if we're using the new category structure
        $hasNewCategories = Product::where('seller_id', $seller->id)
            ->where('status', 'active')
            ->whereNotNull('category_id')
            ->exists();

        // Start building the query
        $query = Product::where('seller_id', $seller->id)
            ->where('status', 'active');

        if ($hasNewCategories) {
            // Always try to find by subcategory slug first (preferred method)
            $subcategory = ProductSubcategory::where('slug', $categorySlug)->first();

            if ($subcategory) {
                $query->where('subcategory_id', $subcategory->id);
                $categoryName = $subcategory->name;
                $categoryId = $subcategory->id;
            } else {
                // Try to find by category slug
                $category = ProductCategory::where('slug', $categorySlug)->first();

                if ($category) {
                    $query->where('category_id', $category->id);
                    $categoryName = $category->name;
                    $categoryId = null;
                } else {
                    // Try to find by detailed category slug
                    $detailedCategory = ProductDetailedCategory::where('slug', $categorySlug)->first();

                    if ($detailedCategory) {
                        $query->where('detailed_category_id', $detailedCategory->id);
                        $categoryName = $detailedCategory->name;
                        $categoryId = null;
                    } else {
                        // Fallback to legacy category or UUID (for backward compatibility)
                        if (is_string($categorySlug) && preg_match('/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i', $categorySlug)) {
                            // It's a UUID, so check if it's a subcategory ID
                            $subcategory = ProductSubcategory::find($categorySlug);
                            if ($subcategory) {
                                $query->where('subcategory_id', $categorySlug);
                                $categoryName = $subcategory->name;
                                $categoryId = $subcategory->id;
                            } else {
                                // Check if it's a detailed category ID
                                $detailedCategory = ProductDetailedCategory::find($categorySlug);
                                if ($detailedCategory) {
                                    $query->where('detailed_category_id', $categorySlug);
                                    $categoryName = $detailedCategory->name;
                                    $categoryId = null;
                                } else {
                                    // Fallback to legacy category
                                    $query->where('category', $categorySlug);
                                    $categoryName = ucfirst($categorySlug);
                                    $categoryId = null;
                                }
                            }
                        } else {
                            // Fallback to legacy category
                            $query->where('category', $categorySlug);
                            $categoryName = ucfirst($categorySlug);
                            $categoryId = null;
                        }
                    }
                }
            }
        } else {
            // Use legacy category field
            $query->where('category', $categorySlug);
            $categoryName = ucfirst($categorySlug);
            $categoryId = null;
        }

        // Apply price range filter
        $priceRange = $request->input('price_range');
        if ($priceRange && $priceRange != 'all') {
            if (strpos($priceRange, '-') !== false) {
                list($min, $max) = explode('-', $priceRange);
                $query->whereBetween('price', [$min, $max]);
            } elseif (strpos($priceRange, '+') !== false) {
                $min = str_replace('+', '', $priceRange);
                $query->where('price', '>=', $min);
            }
        }

        // Apply rating filter
        $rating = $request->input('rating');
        if ($rating && $rating != 'all') {
            $query->where('average_rating', '>=', $rating);
        }

        // Apply sorting
        $sort = $request->input('sort', 'newest');
        switch ($sort) {
            case 'price_low':
                $query->orderBy('price', 'asc');
                break;
            case 'price_high':
                $query->orderBy('price', 'desc');
                break;
            case 'popular':
                $query->orderBy('average_rating', 'desc');
                break;
            case 'newest':
            default:
                $query->orderBy('created_at', 'desc');
                break;
        }

        // Apply pagination - always use 10 items per page
        $products = $query->paginate(10)->withQueryString();

        // Get all categories for the sidebar
        if ($hasNewCategories) {
            // Use the new category structure
            $categoryData = Product::where('seller_id', $seller->id)
                ->where('status', 'active')
                ->whereNotNull('category_id')
                ->with(['productCategory', 'productSubcategory', 'productDetailedCategory'])
                ->get();

            // Group by category and get counts
            $categories = [];
            $categoryProductCounts = [];

            foreach ($categoryData as $product) {
                // Use subcategory name if available, otherwise fallback to category
                $subCatName = $product->productSubcategory ?
                    $product->productSubcategory->name : ($product->productCategory ? $product->productCategory->name : null);

                if ($subCatName && $product->subcategory_id) {
                    $subCat = ProductSubcategory::find($product->subcategory_id);
                    if ($subCat) {
                        // Use slug as the key for better SEO-friendly URLs
                        $subCatSlug = $subCat->slug;
                        if (!isset($categories[$subCatSlug])) {
                            $categories[$subCatSlug] = $subCatName;
                            $categoryProductCounts[$subCatSlug] = 1;
                        } else {
                            $categoryProductCounts[$subCatSlug]++;
                        }
                    }
                }
            }
        } else {
            // Fallback to legacy category field
            $categories = Product::where('seller_id', $seller->id)
                ->where('status', 'active')
                ->select('category')
                ->distinct()
                ->get()
                ->pluck('category');

            // Get product count per category
            $categoryProductCounts = [];
            foreach ($categories as $cat) {
                $categoryProductCounts[$cat] = Product::where('seller_id', $seller->id)
                    ->where('status', 'active')
                    ->where('category', $cat)
                    ->count();
            }
        }

        // Use the actual category name for display
        $category = $categoryName;

        // Pass the category slug for links
        $categorySlugForLinks = $categorySlug;

        return view('store.category', compact('seller', 'products', 'categories', 'category', 'categoryProductCounts', 'hasNewCategories', 'categorySlugForLinks'));
    }

    /**
     * Display a specific product in the store.
     *
     * @param  string  $storeNameSlug
     * @param  string  $productSlug
     * @return \Illuminate\View\View
     */
    public function product($storeNameSlug, $slug)
    {
        // Find the seller by store name using the sellerApplication relationship
        $seller = User::where('is_seller', true)
            ->whereHas('sellerApplication', function ($query) use ($storeNameSlug) {
                $query->where('store_name_slug', $storeNameSlug)
                    ->where('status', 'approved');
            })
            ->with('sellerApplication')
            ->firstOrFail();

        // Add a computed store_slug property to the seller
        $seller->store_slug = $storeNameSlug;

        // Get the specific product with its category relationships
        $product = Product::where('seller_id', $seller->id)
            ->where('slug', $slug)
            ->where('status', 'active')
            ->with(['productCategory', 'productSubcategory', 'productDetailedCategory'])
            ->firstOrFail();

        // Check if the user has already purchased this product
        $alreadyPurchased = false;
        if (auth()->check()) {
            $alreadyPurchased = Order::where('buyer_id', auth()->id())
                ->where('product_id', $product->id)
                ->where('status', 'success')
                ->exists();
        }

        // Check if we're using the new category structure
        $hasNewCategories = $product->category_id || $product->subcategory_id || $product->detailed_category_id;

        // Get related products
        $relatedQuery = Product::where('seller_id', $seller->id)
            ->where('status', 'active')
            ->where('id', '!=', $product->id);

        if ($hasNewCategories) {
            // Try to find related products by subcategory first
            if ($product->subcategory_id) {
                $relatedQuery->where('subcategory_id', $product->subcategory_id);
            } elseif ($product->category_id) {
                // Fallback to category
                $relatedQuery->where('category_id', $product->category_id);
            } else {
                // Fallback to legacy category
                $relatedQuery->where('category', $product->category);
            }
        } else {
            // Use legacy category field
            $relatedQuery->where('category', $product->category);
        }

        $relatedProducts = $relatedQuery->take(4)->get();

        // Check if the user has already purchased any of the related products
        $purchasedProductIds = [];
        if (auth()->check()) {
            $purchasedProductIds = Order::where('buyer_id', auth()->id())
                ->where('status', 'success')
                ->pluck('product_id')
                ->toArray();
        }

        // Get category name for display
        if ($hasNewCategories) {
            if ($product->productDetailedCategory) {
                $categoryName = $product->productDetailedCategory->name;
            } elseif ($product->productSubcategory) {
                $categoryName = $product->productSubcategory->name;
            } elseif ($product->productCategory) {
                $categoryName = $product->productCategory->name;
            } else {
                $categoryName = ucfirst($product->category);
            }
        } else {
            $categoryName = ucfirst($product->category);
        }

        return view('store.product', compact('seller', 'product', 'relatedProducts', 'categoryName', 'hasNewCategories', 'alreadyPurchased', 'purchasedProductIds'));
    }

    /**
     * Display the about page for the store.
     *
     * @param  string  $storeNameSlug
     * @return \Illuminate\View\View
     */
    public function about($storeNameSlug)
    {
        // Find the seller by store name using the sellerApplication relationship
        $seller = User::where('is_seller', true)
            ->whereHas('sellerApplication', function ($query) use ($storeNameSlug) {
                $query->where('store_name_slug', $storeNameSlug)
                    ->where('status', 'approved');
            })
            ->with('sellerApplication')
            ->firstOrFail();

        // Add a computed store_slug property to the seller
        $seller->store_slug = $storeNameSlug;

        // get product count
        $productCount = Product::where('seller_id', $seller->id)
            ->where('status', 'active')
            ->count();

        // Check if we're using the new category structure
        $hasNewCategories = Product::where('seller_id', $seller->id)
            ->where('status', 'active')
            ->whereNotNull('category_id')
            ->exists();

        // Get product categories for this seller
        $products = Product::where('seller_id', $seller->id)
            ->where('status', 'active')
            ->with(['productCategory', 'productSubcategory', 'productDetailedCategory'])
            ->get();

        // Extract unique category names with fallbacks
        $categories = [];

        if ($hasNewCategories) {
            // Use the new category structure
            foreach ($products as $product) {
                if ($product->productDetailedCategory) {
                    $categories[] = $product->productDetailedCategory->name;
                } elseif ($product->productSubcategory) {
                    $categories[] = $product->productSubcategory->name;
                } elseif ($product->productCategory) {
                    $categories[] = $product->productCategory->name;
                } elseif ($product->category) {
                    // Fallback to legacy category if available
                    $categories[] = ucfirst($product->category);
                } else {
                    // Last resort fallback
                    $categories[] = 'Digital Products';
                }
            }
        } else {
            // Get product categories for this seller using legacy category field
            foreach ($products as $product) {
                if ($product->category) {
                    $categories[] = ucfirst($product->category);
                } else {
                    $categories[] = 'Digital Products';
                }
            }
        }

        // Remove duplicates and empty values
        $categories = array_unique(array_filter($categories));

        // If still no categories, add a default
        if (empty($categories) && $productCount > 0) {
            $categories[] = 'Digital Products';
        }

        return view('store.about', compact('seller', 'productCount', 'categories', 'hasNewCategories'));
    }

    /**
     * Search for products in the store.
     *
     * @param  string  $storeNameSlug
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function search($storeNameSlug, Request $request)
    {
        // Find the seller by store name using the sellerApplication relationship
        $seller = User::where('is_seller', true)
            ->whereHas('sellerApplication', function ($query) use ($storeNameSlug) {
                $query->where('store_name_slug', $storeNameSlug)
                    ->where('status', 'approved');
            })
            ->with('sellerApplication')
            ->firstOrFail();

        // Add a computed store_slug property to the seller
        $seller->store_slug = $storeNameSlug;

        $query = $request->input('query');

        // Search for products
        $products = Product::where('seller_id', $seller->id)
            ->where('status', 'active')
            ->where(function ($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                    ->orWhere('description', 'like', "%{$query}%")
                    ->orWhere('category', 'like', "%{$query}%");
            })
            ->paginate(10);

        // Get all categories for the sidebar
        // Check if we're using the new category structure
        $hasNewCategories = Product::where('seller_id', $seller->id)
            ->where('status', 'active')
            ->whereNotNull('category_id')
            ->exists();

        if ($hasNewCategories) {
            // Use the new category structure
            $categoryData = Product::where('seller_id', $seller->id)
                ->where('status', 'active')
                ->whereNotNull('category_id')
                ->with(['productCategory', 'productSubcategory', 'productDetailedCategory'])
                ->get();

            // Group by category and get counts
            $categories = [];
            $categoryProductCounts = [];

            foreach ($categoryData as $product) {
                // Use subcategory name if available, otherwise fallback to category
                $categoryName = $product->productSubcategory ?
                    $product->productSubcategory->name : ($product->productCategory ? $product->productCategory->name : null);

                if ($categoryName && $product->subcategory_id) {
                    $subCat = ProductSubcategory::find($product->subcategory_id);
                    if ($subCat) {
                        // Use slug as the key for better SEO-friendly URLs
                        $subCatSlug = $subCat->slug;
                        if (!isset($categories[$subCatSlug])) {
                            $categories[$subCatSlug] = $categoryName;
                            $categoryProductCounts[$subCatSlug] = 1;
                        } else {
                            $categoryProductCounts[$subCatSlug]++;
                        }
                    }
                }
            }
        } else {
            // Fallback to legacy category field
            $categories = Product::where('seller_id', $seller->id)
                ->where('status', 'active')
                ->select('category')
                ->distinct()
                ->get()
                ->pluck('category');

            // Get product count per category
            $categoryProductCounts = [];
            foreach ($categories as $category) {
                $categoryProductCounts[$category] = Product::where('seller_id', $seller->id)
                    ->where('status', 'active')
                    ->where('category', $category)
                    ->count();
            }
        }

        return view('store.search', compact('seller', 'products', 'categories', 'categoryProductCounts', 'query', 'hasNewCategories'));
    }

    /**
     * Serve the store logo from storage.
     *
     * @param  string  $storeSlug
     * @return \Illuminate\Http\Response
     */
    public function getStoreLogo($storeSlug)
    {
        // Find the seller application by store slug
        $sellerApplication = SellerApplication::where('store_name_slug', $storeSlug)
            ->where('status', 'approved')
            ->firstOrFail();

        // Check if the store logo exists
        if (!$sellerApplication->store_logo) {
            // Return a default image if the store logo doesn't exist
            return response()->file(public_path('images/default-store-logo.png'));
        }

        // Check if the logo is stored in private or public storage
        if (str_starts_with($sellerApplication->store_logo, 'private/')) {
            // Check if the file exists in local storage
            if (!Storage::disk('local')->exists($sellerApplication->store_logo)) {
                return response()->file(public_path('images/default-store-logo.png'));
            }

            // Serve the file from private storage
            return response()->file(storage_path('app/' . $sellerApplication->store_logo));
        } else {
            // Check if the file exists in public storage
            if (!Storage::disk('public')->exists($sellerApplication->store_logo)) {
                return response()->file(public_path('images/default-store-logo.png'));
            }

            // Serve the file from public storage
            return response()->file(storage_path('app/public/' . $sellerApplication->store_logo));
        }
    }
}
