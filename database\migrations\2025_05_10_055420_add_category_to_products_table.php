<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // Keep the legacy category field for backward compatibility
            // Add new category fields with proper foreign keys
            $table->foreignUuid('category_id')->nullable()->constrained('product_categories')->nullOnDelete();
            $table->foreignUuid('subcategory_id')->nullable()->constrained('product_subcategories')->nullOnDelete();
            $table->foreignUuid('detailed_category_id')->nullable()->constrained('product_detailed_categories')->nullOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropForeign(['category_id']);
            $table->dropForeign(['subcategory_id']);
            $table->dropForeign(['detailed_category_id']);
            $table->dropColumn(['category_id', 'subcategory_id', 'detailed_category_id']);
        });
    }
};
