<?php

namespace App\Http\Controllers\Seller;

use App\Http\Controllers\Controller;
use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use App\Models\Product;
use App\Models\SellerApplication;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class ProductController extends Controller
{
    public function index(Request $request)
    {
        $query = Product::where('seller_id', auth()->id());

        if ($search = $request->input('search')) {
            $query->where('name', 'like', "%{$search}%")
                ->orWhere('description', 'like', "%{$search}%");
        }

        if ($status = $request->input('status')) {
            if ($status !== 'all') {
                $query->where('status', $status);
            }
        }

        // Add withCount for orders with 'success' status to show correct sales numbers
        $query->withCount(['orders' => function ($query) {
            $query->where('status', 'success');
        }]);

        $products = $query->paginate(10);
        return view('seller.products.index', compact('products'));
    }

    public function create()
    {
        // Get all categories with their subcategories and detailed categories
        $categoryTree = \App\Models\ProductCategory::active()
            ->with(['activeSubcategories', 'activeSubcategories.activeDetailedCategories'])
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get();

        // For backward compatibility, generate legacy category groups from the database
        $categoryGroups = [];
        $legacyCategoryMapping = [];
        $legacySubcategoryMapping = [];

        foreach ($categoryTree as $category) {
            $categoryGroups[$category->name] = [];

            foreach ($category->activeSubcategories as $subcategory) {
                try {
                    // Try to use the legacy_code if available, otherwise use the slug or create one from the name
                    // This is wrapped in a try-catch in case the legacy_code column doesn't exist yet
                    $legacyCode = $subcategory->legacy_code;
                    if (empty($legacyCode)) {
                        $legacyCode = $subcategory->getLegacyCategoryAttribute() ?? \Illuminate\Support\Str::slug($subcategory->name);
                    }
                } catch (\Exception $e) {
                    // If there's an error (like the column doesn't exist), use the slug or create one from the name
                    $legacyCode = $subcategory->getLegacyCategoryAttribute() ?? \Illuminate\Support\Str::slug($subcategory->name);
                }

                // Add to the category groups
                $categoryGroups[$category->name][$legacyCode] = $subcategory->name;

                // Build the mappings for JavaScript
                $legacyCategoryMapping[$legacyCode] = $category->name;
                $legacySubcategoryMapping[$legacyCode] = $subcategory->name;
            }
        }

        return view('seller.products.create', compact('categoryGroups', 'categoryTree', 'legacyCategoryMapping', 'legacySubcategoryMapping'));
    }

    public function store(Request $request)
    {
        // We don't need this anymore as we're not validating against a fixed list of legacy codes
        // $validLegacyCodes = \App\Models\ProductSubcategory::pluck('legacy_code')->filter()->toArray();

        // Basic validation rules
        $rules = [
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'category' => 'required|string',
            'category_id' => 'nullable|exists:product_categories,id',
            'subcategory_id' => 'nullable|exists:product_subcategories,id',
            'detailed_category_id' => 'nullable|exists:product_detailed_categories,id',
            'price' => 'required|numeric|min:5000',
            'discount_price' => 'nullable|numeric|min:5000|lt:price',
            'status' => 'required|in:draft,active',
        ];

        // Filter out images that exceed size limit or exceed max count
        $validImages = [];
        if ($request->hasFile('images')) {
            $imageCount = 0;
            foreach ($request->file('images') as $index => $image) {
                if ($imageCount >= 10) { // Maximum 10 images
                    break;
                }
                if ($image->getSize() <= 2 * 1024 * 1024) { // 2MB in bytes
                    $validImages[] = $image;
                    $imageCount++;
                }
            }
        }

        // Filter out files that exceed size limit or exceed max count
        $validFiles = [];
        if ($request->hasFile('files')) {
            $fileCount = 0;
            foreach ($request->file('files') as $index => $file) {
                if ($fileCount >= 5) { // Maximum 5 files
                    break;
                }
                if ($file->getSize() <= 20 * 1024 * 1024) { // 20MB in bytes
                    $validFiles[] = $file;
                    $fileCount++;
                }
            }
        }

        // If status is active, check if we have at least one valid image and file
        if ($request->input('status') === 'active') {
            // For new products, we simply check if there are valid uploads
            // Check if we have at least one valid image
            if (count($validImages) === 0) {
                return response()->json([
                    'success' => false,
                    'errors' => ['images' => ['⚠️ At least one valid image (max 2MB each, up to 10 images) is required when publishing a product.']]
                ], 422);
            }

            // Check if the number of images exceeds the limit
            if (count($validImages) > 10) {
                return response()->json([
                    'success' => false,
                    'errors' => ['images' => ['⚠️ You can only upload a maximum of 10 images per product. You attempted to upload ' . count($validImages) . ' images.']]
                ], 422);
            }

            if (count($validFiles) === 0) {
                return response()->json([
                    'success' => false,
                    'errors' => ['files' => ['⚠️ At least one valid file (max 20MB each, up to 5 files) is required when publishing a product.']]
                ], 422);
            }

            // Check if the number of files exceeds the limit
            if (count($validFiles) > 5) {
                return response()->json([
                    'success' => false,
                    'errors' => ['files' => ['⚠️ You can only upload a maximum of 5 files per product. You attempted to upload ' . count($validFiles) . ' files.']]
                ], 422);
            }
        }

        // Log validation for debugging
        Log::info('Product create validation', [
            'status' => $request->input('status'),
            'valid_images' => count($validImages),
            'valid_files' => count($validFiles)
        ]);

        // Validate the basic fields
        $validated = $request->validate($rules);

        $productData = $validated;
        $productData['seller_id'] = auth()->id();

        // Generate unique slug
        $baseSlug = Str::slug($request->name); // e.g., "survey ipds" -> "survey-ipds"
        $slug = $baseSlug;
        $counter = 1;

        // Check for duplicate slugs and append a counter if needed
        while (Product::where('slug', $slug)->exists()) {
            $slug = "{$baseSlug}-{$counter}";
            $counter++;
        }
        $productData['slug'] = $slug;

        // For backward compatibility, store the first valid image in the image field
        if (count($validImages) > 0) {
            $path = $validImages[0]->store('products/images', 'public');
            $productData['image'] = $path;
        }

        // Handle files upload - only use valid files
        $files = [];
        foreach ($validFiles as $file) {
            $path = $file->store('products/files', 'public');
            $files[] = [
                'name' => $file->getClientOriginalName(),
                'path' => $path,
                'size' => $file->getSize(),
            ];
        }
        $productData['files'] = json_encode($files);

        // Create the product
        $product = Product::create($productData);

        // Handle multiple image uploads - only use valid images
        // Get the order of images from the request
        $imageOrder = $request->input('image_order', []);
        $hasCustomOrder = !empty($imageOrder);

        // Process each valid uploaded image
        $uploadedImages = [];
        foreach ($validImages as $index => $image) {
            $path = $image->store('products/images', 'public');

            // Store the uploaded image info for later processing
            $uploadedImages[] = [
                'path' => $path,
                'original_index' => $index,
                'name' => $image->getClientOriginalName()
            ];
        }

        // If we have a custom order, reorder the uploaded images
        if ($hasCustomOrder) {
            $orderedImages = [];
            foreach ($imageOrder as $imageName) {
                // Find the image with this name
                foreach ($uploadedImages as $uploadedImage) {
                    if ($uploadedImage['name'] === $imageName) {
                        $orderedImages[] = $uploadedImage;
                        break;
                    }
                }
            }

            // Add any images that weren't in the order array
            foreach ($uploadedImages as $uploadedImage) {
                if (!in_array($uploadedImage, $orderedImages)) {
                    $orderedImages[] = $uploadedImage;
                }
            }

            $uploadedImages = $orderedImages;
        }

        // Now create the product image records in the correct order
        foreach ($uploadedImages as $index => $uploadedImage) {
            $product->images()->create([
                'path' => $uploadedImage['path'],
                'is_primary' => $index === 0, // First image is primary
                'sort_order' => $index,
            ]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Product created successfully!',
            'redirect' => route('seller.products.index'),
        ], 200);
    }

    public function edit(Product $product)
    {
        // Get all categories with their subcategories and detailed categories
        $categoryTree = \App\Models\ProductCategory::active()
            ->with(['activeSubcategories', 'activeSubcategories.activeDetailedCategories'])
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get();

        // For backward compatibility, generate legacy category groups from the database
        $categoryGroups = [];
        $legacyCategoryMapping = [];
        $legacySubcategoryMapping = [];

        foreach ($categoryTree as $category) {
            $categoryGroups[$category->name] = [];

            foreach ($category->activeSubcategories as $subcategory) {
                try {
                    // Try to use the legacy_code if available, otherwise use the slug or create one from the name
                    // This is wrapped in a try-catch in case the legacy_code column doesn't exist yet
                    $legacyCode = $subcategory->legacy_code;
                    if (empty($legacyCode)) {
                        $legacyCode = $subcategory->getLegacyCategoryAttribute() ?? \Illuminate\Support\Str::slug($subcategory->name);
                    }
                } catch (\Exception $e) {
                    // If there's an error (like the column doesn't exist), use the slug or create one from the name
                    $legacyCode = $subcategory->getLegacyCategoryAttribute() ?? \Illuminate\Support\Str::slug($subcategory->name);
                }

                // Add to the category groups
                $categoryGroups[$category->name][$legacyCode] = $subcategory->name;

                // Build the mappings for JavaScript
                $legacyCategoryMapping[$legacyCode] = $category->name;
                $legacySubcategoryMapping[$legacyCode] = $subcategory->name;
            }
        }

        return view('seller.products.edit', compact('product', 'categoryGroups', 'categoryTree', 'legacyCategoryMapping', 'legacySubcategoryMapping'));
    }

    public function update(Request $request, Product $product)
    {
        // Basic validation rules
        $rules = [
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'category' => 'required|string',
            'category_id' => 'nullable|exists:product_categories,id',
            'subcategory_id' => 'nullable|exists:product_subcategories,id',
            'detailed_category_id' => 'nullable|exists:product_detailed_categories,id',
            'price' => 'required|numeric|min:5000',
            'discount_price' => 'nullable|numeric|min:5000|lt:price',
            'status' => 'required|in:draft,active',
        ];

        // Filter out images that exceed size limit or exceed max count
        $validImages = [];
        if ($request->hasFile('images')) {
            $imageCount = 0;
            foreach ($request->file('images') as $index => $image) {
                if ($imageCount >= 10) { // Maximum 10 images
                    break;
                }
                if ($image->getSize() <= 2 * 1024 * 1024) { // 2MB in bytes
                    $validImages[] = $image;
                    $imageCount++;
                }
            }
        }

        // Filter out files that exceed size limit or exceed max count
        $validFiles = [];
        if ($request->hasFile('files')) {
            $fileCount = 0;
            foreach ($request->file('files') as $index => $file) {
                if ($fileCount >= 5) { // Maximum 5 files
                    break;
                }
                if ($file->getSize() <= 20 * 1024 * 1024) { // 20MB in bytes
                    $validFiles[] = $file;
                    $fileCount++;
                }
            }
        }

        // Get existing files
        $existingFiles = json_decode($product->files, true) ?: [];
        $removeFiles = array_filter(
            $request->input('remove_files', []),
            fn($v) => is_string($v) || is_int($v)
        );
        $remainingFiles = array_diff_key($existingFiles, array_flip($removeFiles));

        // If status is active, check if we have at least one valid image and file
        if ($request->input('status') === 'active') {
            try {
                // Get the count of existing images that aren't being removed
                $existingImagesCount = $product->images()->count();
                $removeImages = $request->input('remove_images', []);
                if (!empty($removeImages)) {
                    $existingImagesCount = $product->images()->whereNotIn('id', $removeImages)->count();
                }

                // Check if the total number of images (existing + new) exceeds the limit
                if ($existingImagesCount + count($validImages) > 10) {
                    return response()->json([
                        'success' => false,
                        'errors' => ['images' => ['⚠️ You can only have a maximum of 10 images per product. This product will have ' . ($existingImagesCount + count($validImages)) . ' images after upload.']]
                    ], 422);
                }

                // Check if the total number of files (existing + new) exceeds the limit
                $existingFilesCount = count($remainingFiles);
                if ($existingFilesCount + count($validFiles) > 5) {
                    return response()->json([
                        'success' => false,
                        'errors' => ['files' => ['⚠️ You can only have a maximum of 5 files per product. This product will have ' . ($existingFilesCount + count($validFiles)) . ' files after upload.']]
                    ], 422);
                }

                // Only require new images if there are no existing ones after removals
                if (count($validImages) === 0 && $existingImagesCount === 0) {
                    return response()->json([
                        'success' => false,
                        'errors' => ['images' => ['⚠️ At least one valid image (max 2MB each, up to 10 images) is required when publishing a product. Please ensure your images meet these requirements.']]
                    ], 422);
                }

                // Check if we have at least one file (existing or new valid upload)
                if (empty($remainingFiles) && count($validFiles) === 0) {
                    return response()->json([
                        'success' => false,
                        'errors' => ['files' => ['⚠️ At least one valid file (max 20MB each, up to 5 files) is required when publishing a product. Please ensure your files meet these requirements.']]
                    ], 422);
                }
            } catch (\Exception $e) {
                // If there's an error (like product is null), log it and require at least one valid file and image
                Log::error('Error in product update validation', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);

                if (count($validImages) === 0) {
                    return response()->json([
                        'success' => false,
                        'errors' => ['images' => ['⚠️ At least one valid image (max 2MB each, up to 10 images) is required when publishing a product. Please ensure your images meet these requirements.']]
                    ], 422);
                }

                if (count($validFiles) === 0) {
                    return response()->json([
                        'success' => false,
                        'errors' => ['files' => ['⚠️ At least one valid file (max 20MB each, up to 5 files) is required when publishing a product. Please ensure your files meet these requirements.']]
                    ], 422);
                }
            }
        }

        // Log the counts for debugging
        Log::info('Product update validation', [
            'product_id' => $product->id,
            'status' => $request->input('status'),
            'existing_images' => $product->images()->count(),
            'valid_images' => count($validImages),
            'remaining_files' => count($remainingFiles),
            'valid_files' => count($validFiles)
        ]);

        // Validate the basic fields
        $validated = $request->validate($rules);

        $productData = $validated;

        // Generate unique slug, excluding the current product
        $baseSlug = Str::slug($request->name);
        $slug = $baseSlug;
        $counter = 1;

        // Check for duplicate slugs, excluding the current product
        while (Product::where('slug', $slug)->where('id', '!=', $product->id)->exists()) {
            $slug = "{$baseSlug}-{$counter}";
            $counter++;
        }
        $productData['slug'] = $slug;

        // Handle images to be removed
        if ($removeImageIds = $request->input('remove_images', [])) {
            foreach ($removeImageIds as $imageId) {
                $image = $product->images()->find($imageId);
                if ($image) {
                    Storage::disk('public')->delete($image->path);
                    $image->delete();
                }
            }
        }

        // Handle image reordering
        if ($imageOrder = $request->input('image_order', [])) {
            foreach ($imageOrder as $index => $imageId) {
                $product->images()->where('id', $imageId)->update(['sort_order' => $index]);
            }
        }

        // Handle primary image setting
        if ($primaryImageId = $request->input('primary_image_id')) {
            // First, set all images as non-primary
            $product->images()->update(['is_primary' => false]);

            // Then set the selected image as primary
            $primaryImage = $product->images()->find($primaryImageId);
            if ($primaryImage) {
                $primaryImage->update(['is_primary' => true]);

                // Also update the main image field for backward compatibility
                $productData['image'] = $primaryImage->path;
            }
        }

        // Handle new image uploads - only use valid images
        if (count($validImages) > 0) {
            // For backward compatibility, update the main image field with the first valid image
            // Only update the main image field if there's no primary image yet
            // or if the main image field is empty
            $hasPrimaryImage = $product->images()->where('is_primary', true)->exists();

            if (!$hasPrimaryImage || !$product->image) {
                $path = $validImages[0]->store('products/images', 'public');
                $productData['image'] = $path;
            }

            // Process all valid images
            $existingImagesCount = $product->images()->count();

            foreach ($validImages as $index => $image) {
                $path = $image->store('products/images', 'public');

                // Determine if this should be the primary image
                $isPrimary = false;

                // Make it primary if it's the first image and no primary exists
                if ($index === 0 && $product->images()->where('is_primary', true)->count() === 0) {
                    $isPrimary = true;
                }

                // Create a product image record
                $product->images()->create([
                    'path' => $path,
                    'is_primary' => $isPrimary,
                    'sort_order' => $existingImagesCount + $index,
                ]);
            }
        } elseif ($request->input('remove_all_images')) {
            // Delete all images
            foreach ($product->images as $image) {
                Storage::disk('public')->delete($image->path);
            }
            $product->images()->delete();

            // Also clear the main image field
            if ($product->image) {
                Storage::disk('public')->delete($product->image);
                $productData['image'] = null;
            }
        }

        // Handle existing files removal
        $existingFiles = json_decode($product->files, true) ?: [];
        if ($removeFiles = $request->input('remove_files', [])) {
            foreach ($removeFiles as $index) {
                if (isset($existingFiles[$index])) {
                    Storage::disk('public')->delete($existingFiles[$index]['path']);
                    unset($existingFiles[$index]);
                }
            }
            $existingFiles = array_values($existingFiles); // Reindex array
        }

        // Handle new files upload - only use valid files
        foreach ($validFiles as $file) {
            $path = $file->store('products/files', 'public');
            $existingFiles[] = [
                'name' => $file->getClientOriginalName(),
                'path' => $path,
                'size' => $file->getSize(),
            ];
        }
        $productData['files'] = json_encode($existingFiles);

        $product->update($productData);

        return response()->json([
            'success' => true,
            'message' => 'Product updated successfully!',
            'redirect' => route('seller.products.index'),
        ], 200);
    }

    public function destroy(Product $product)
    {
        // Delete associated files
        if ($product->image) {
            Storage::disk('public')->delete($product->image);
        }

        // Delete all product images
        foreach ($product->images as $image) {
            Storage::disk('public')->delete($image->path);
        }

        // Delete product files
        $files = json_decode($product->files, true) ?: [];
        foreach ($files as $file) {
            Storage::disk('public')->delete($file['path']);
        }

        $product->delete();

        return redirect()->route('seller.products.index')->with('success', 'Product deleted successfully!');
    }

    /**
     * Preview a product before it's published.
     *
     * @param  \App\Models\Product  $product
     * @return \Illuminate\Http\Response
     */
    public function preview(Product $product)
    {
        // Get the specific product with its category relationships
        $product = Product::where('seller_id', auth()->id())
            ->where('slug', $product->slug)
            ->where('status', 'active')
            ->with(['productCategory', 'productSubcategory', 'productDetailedCategory'])
            ->firstOrFail();

        // Check if we're using the new category structure
        $hasNewCategories = $product->category_id || $product->subcategory_id || $product->detailed_category_id;

        // Get related products
        $relatedQuery = Product::where('seller_id', auth()->id())
            ->where('status', 'active')
            ->where('id', '!=', $product->id);

        if ($hasNewCategories) {
            // Try to find related products by subcategory first
            if ($product->subcategory_id) {
                $relatedQuery->where('subcategory_id', $product->subcategory_id);
            } elseif ($product->category_id) {
                // Fallback to category
                $relatedQuery->where('category_id', $product->category_id);
            } else {
                // Fallback to legacy category
                $relatedQuery->where('category', $product->category);
            }
        } else {
            // Use legacy category field
            $relatedQuery->where('category', $product->category);
        }

        $relatedProducts = $relatedQuery->take(4)->get();

        // Check if the user has already purchased any of the related products
        $purchasedProductIds = [];
        if (auth()->check()) {
            $purchasedProductIds = Order::where('buyer_id', auth()->id())
                ->where('status', 'success')
                ->pluck('product_id')
                ->toArray();
        }

        $seller = SellerApplication::where('user_id', auth()->id())->firstOrFail();

        $seller->store_slug = $seller->store_name_slug;

        // Get category name for display
        if ($hasNewCategories) {
            if ($product->productDetailedCategory) {
                $categoryName = $product->productDetailedCategory->name;
            } elseif ($product->productSubcategory) {
                $categoryName = $product->productSubcategory->name;
            } elseif ($product->productCategory) {
                $categoryName = $product->productCategory->name;
            } else {
                $categoryName = ucfirst($product->category);
            }
        } else {
            $categoryName = ucfirst($product->category);
        }

        return view('seller.products.preview', compact('seller', 'product', 'relatedProducts', 'categoryName', 'hasNewCategories', 'purchasedProductIds'));
    }
}
