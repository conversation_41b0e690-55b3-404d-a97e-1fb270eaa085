<?php

namespace App\Http\Controllers;

use App\Models\Cart;
use App\Models\Order;
use App\Models\Product;
use App\Models\User;
use App\Models\SellerApplication;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rules;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Response;
use ZipArchive;
use Illuminate\Support\Str;

class UserController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display the user's dashboard.
     *
     * @return \Illuminate\View\View
     */
    public function dashboard()
    {
        $cartItemCount = 0;
        $cart = Cart::where('user_id', Auth::id())->first();
        if ($cart) {
            $cartItemCount = $cart->items->count();
        }

        // Calculate total purchases amount
        $totalPurchases = Order::where('buyer_id', Auth::id())
            ->where('status', 'success')
            ->sum('amount');

        // Get recent purchases for activity feed
        $recentActivity = Order::where('buyer_id', Auth::id())
            ->where('status', 'success')
            ->with('product')
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        return view('users.dashboard', compact('cartItemCount', 'totalPurchases', 'recentActivity'));
    }

    /**
     * Get stores organized by their main category
     *
     * @return array
     */
    private function getStoresByCategory()
    {
        // Get all active categories
        $categories = \App\Models\ProductCategory::where('is_active', true)
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get();

        $storesByCategory = [];

        foreach ($categories as $category) {
            // Find sellers with products in this category
            $sellers = User::where('is_seller', true)
                ->whereHas('sellerApplication', function($query) {
                    $query->where('status', 'approved');
                })
                ->whereHas('products', function($query) use ($category) {
                    $query->where('status', 'active')
                        ->where('category_id', $category->id);
                })
                ->withCount(['products' => function($query) {
                    $query->where('status', 'active');
                }])
                ->having('products_count', '>', 0)
                ->with('sellerApplication')
                ->take(4)
                ->get();

            if ($sellers->count() > 0) {
                $storeData = [];

                foreach ($sellers as $seller) {
                    $store = $seller->sellerApplication;
                    if (!$store) continue;

                    $storeData[] = [
                        'name' => $store->store_name,
                        'slug' => $store->store_name_slug,
                        'description' => \Illuminate\Support\Str::limit($store->store_description, 100),
                        'logo' => $store->store_logo ? asset('storage/' . $store->store_logo) : null,
                        'product_count' => $seller->products_count,
                        'user_avatar' => $seller->avatar ? asset('storage/' . $seller->avatar) : null,
                        'user_name' => $seller->name
                    ];
                }

                if (!empty($storeData)) {
                    $storesByCategory[] = [
                        'category' => $category,
                        'stores' => $storeData
                    ];
                }
            }
        }

        return $storesByCategory;
    }

    /**
     * Display the user's purchases.
     *
     * @return \Illuminate\View\View
     */
    public function purchases()
    {
        try {
            $purchases = Order::where('buyer_id', Auth::id())
                ->where('status', 'success')
                ->with([
                    'product',
                    'product.productCategory',
                    'product.productSubcategory',
                    'product.productDetailedCategory'
                ])
                ->orderBy('created_at', 'desc')
                ->get();
        } catch (\Exception $e) {
            // Log the error
            Log::error('Error fetching purchases: ' . $e->getMessage());

            // Return empty collection if there's an error
            $purchases = collect([]);
        }

        $cartItemCount = 0;
        $cart = Cart::where('user_id', Auth::id())->first();
        if ($cart) {
            $cartItemCount = $cart->items->count();
        }

        return view('users.purchases', compact('purchases', 'cartItemCount'));
    }

    /**
     * Display the user's profile.
     *
     * @return \Illuminate\View\View
     */
    public function profile()
    {
        $cartItemCount = 0;
        $cart = Cart::where('user_id', Auth::id())->first();
        if ($cart) {
            $cartItemCount = $cart->items->count();
        }

        return view('users.profile', compact('cartItemCount'));
    }

    /**
     * Update the user's profile.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateProfile(Request $request)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users,email,' . Auth::id()],
            'phone' => ['nullable', 'string', 'max:20'],
            'bio' => ['nullable', 'string', 'max:1000'],
            'avatar' => ['nullable', 'image', 'mimes:jpeg,png,jpg,gif', 'max:2048'],
        ]);

        $user = Auth::user();

        // Prepare user data for update
        $userData = [
            'name' => $request->name,
            'phone' => $request->phone,
            'bio' => $request->bio,
        ];

        // Handle avatar upload if provided
        if ($request->hasFile('avatar')) {
            // Delete old avatar if exists and is not the default
            if ($user->avatar && !str_contains($user->avatar, 'default-avatar')) {
                \Illuminate\Support\Facades\Storage::disk('public')->delete($user->avatar);
            }

            // Store avatar in a user-specific folder
            $userId = $user->id;
            $path = $request->file('avatar')->store("avatars/{$userId}", 'public');
            $userData['avatar'] = $path;
        }

        // Update user data using update method
        User::where('id', $user->id)->update($userData);

        return redirect()->route('user.profile')->with('success', 'Profile updated successfully!');
    }

    /**
     * Update the user's password.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updatePassword(Request $request)
    {
        $request->validate([
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
        ]);

        $user = Auth::user();

        // Update password using update method
        User::where('id', $user->id)->update([
            'password' => Hash::make($request->password)
        ]);

        return redirect()->route('user.profile')->with('success', 'Password updated successfully!');
    }

    /**
     * Display the user's settings.
     *
     * @return \Illuminate\View\View
     */
    public function settings()
    {
        $cartItemCount = 0;
        $cart = Cart::where('user_id', Auth::id())->first();
        if ($cart) {
            $cartItemCount = $cart->items->count();
        }

        return view('users.settings', compact('cartItemCount'));
    }

    /**
     * Update the user's settings.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateSettings(Request $request)
    {
        $user = Auth::user();

        // Update notification preferences using update method
        User::where('id', $user->id)->update([
            'notification_order' => $request->has('email_order_updates'),
            'notification_marketing' => $request->has('email_promotions')
        ]);

        return redirect()->route('user.settings')->with('success', 'Settings updated successfully!');
    }

    public function productDownload(Request $request, Product $product)
    {
        // cek order is success
        $order = Order::where('buyer_id', Auth::id())
            ->where('product_id', $product->id)
            ->where('status', 'success')
            ->first();
        if (!$order)
            return redirect()->back()->with('error', 'You do not have permission to download this product');

        // download file
        $files = json_decode($product->files, true);

        if (count($files) === 1) {
            // Single file download
            $file = $files[0];

            $path = Storage::path('public/' . $file['path']);

            if (!file_exists($path)) {
                return redirect()->back()->with('error', 'Oops, file not found');
            }

            return response()->download($path, $file['name']);
        }

        // Multiple files: create zip
        $zip = new ZipArchive;

        $fileName = Str::slug('Digitora ' . $product->name) . '.zip';

        $zipOutput = Storage::path('public/' . $fileName);
        if ($zip->open($zipOutput, ZipArchive::CREATE) === TRUE) {

            foreach ($files as $f) {
                $filePath = Storage::path('public/' . $f['path']);

                if (Storage::exists('public/' . $f['path']))
                    $zip->addFile($filePath, basename($filePath));
            }

            $zip->close();
        } else {
            return redirect()->back()->with('error', 'Failed to download files');
        }

        return response()->download($zipOutput)->deleteFileAfterSend(true);
    }

    /**
     * Handle the seller dashboard redirection.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function sellerDashboard()
    {
        $user = Auth::user();

        // Check if the user is a seller
        if ($user->is_seller) {
            // Redirect to the seller dashboard
            return redirect()->route('seller.dashboard');
        }

        // Check if the user has a pending seller application
        $application = SellerApplication::where('user_id', $user->id)->first();

        if ($application) {
            // If application exists, check its status
            if ($application->status === 'approved') {
                // If approved but is_seller flag is not set, update it
                if (!$user->is_seller) {
                    User::where('id', $user->id)->update(['is_seller' => true]);
                }
                return redirect()->route('seller.dashboard');
            } elseif ($application->status === 'pending') {
                return redirect()->route('seller.pending');
            } elseif ($application->status === 'rejected') {
                return redirect()->route('seller.rejected');
            }
        }

        // If no application exists, redirect to the seller application page
        return redirect()->route('seller.apply');
    }

    /**
     * Display the browse stores page.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function browseStores(Request $request)
    {
        $cartItemCount = 0;
        $cart = Cart::where('user_id', Auth::id())->first();
        if ($cart) {
            $cartItemCount = $cart->items->count();
        }

        // Get all active detailed categories
        $detailedCategories = \App\Models\ProductDetailedCategory::where('is_active', true)
            ->with(['subcategory.category'])
            ->orderBy('name')
            ->get();

        // Get selected detailed category if any
        $selectedCategorySlug = $request->input('category');
        $selectedDetailedCategory = null;

        if ($selectedCategorySlug) {
            $selectedDetailedCategory = \App\Models\ProductDetailedCategory::where('slug', $selectedCategorySlug)->first();
        }

        // Build query for stores
        $query = User::where('is_seller', true)
            ->whereHas('sellerApplication', function($query) {
                $query->where('status', 'approved');
            })
            ->whereHas('products', function($query) {
                $query->where('status', 'active');
            })
            ->withCount(['products' => function($query) {
                $query->where('status', 'active');
            }])
            ->with('sellerApplication');

        // Filter by detailed category if selected
        if ($selectedDetailedCategory) {
            $query->whereHas('products', function($query) use ($selectedDetailedCategory) {
                $query->where('status', 'active')
                    ->where('detailed_category_id', $selectedDetailedCategory->id);
            });
        }

        // Apply sorting
        $sort = $request->input('sort', 'popular');
        switch ($sort) {
            case 'name_asc':
                $query->join('seller_applications', 'users.id', '=', 'seller_applications.user_id')
                    ->orderBy('seller_applications.store_name', 'asc')
                    ->select('users.*');
                break;
            case 'name_desc':
                $query->join('seller_applications', 'users.id', '=', 'seller_applications.user_id')
                    ->orderBy('seller_applications.store_name', 'desc')
                    ->select('users.*');
                break;
            case 'newest':
                $query->orderBy('users.created_at', 'desc');
                break;
            case 'popular':
            default:
                $query->orderByDesc('products_count');
                break;
        }

        // Paginate results
        $stores = $query->paginate(12)->withQueryString();

        // Format store data for the view
        $formattedStores = [];
        foreach ($stores as $seller) {
            $store = $seller->sellerApplication;
            if (!$store) continue;

            // Get product data for this seller
            $products = Product::where('seller_id', $seller->id)
                ->where('status', 'active')
                ->get();

            // Count products by detailed category
            $mainDetailedCategory = null;
            $detailedCategoryProductCount = 0;

            // If we have a selected detailed category, count products in that category
            if ($selectedDetailedCategory) {
                $detailedCategoryProductCount = $products->where('detailed_category_id', $selectedDetailedCategory->id)->count();
                $mainDetailedCategory = $selectedDetailedCategory;
            }
            // Otherwise, find the main detailed category (with most products)
            else if ($products->isNotEmpty()) {
                $detailedCategoryGroups = $products->groupBy('detailed_category_id');

                // Find the detailed category with the most products
                $maxCount = 0;
                $mainDetailedCategoryId = null;

                foreach ($detailedCategoryGroups as $detailedCategoryId => $categoryProducts) {
                    // Skip null detailed_category_id
                    if (!$detailedCategoryId) continue;

                    $count = $categoryProducts->count();
                    if ($count > $maxCount) {
                        $maxCount = $count;
                        $mainDetailedCategoryId = $detailedCategoryId;
                    }
                }

                if ($mainDetailedCategoryId) {
                    $mainDetailedCategory = \App\Models\ProductDetailedCategory::find($mainDetailedCategoryId);
                    $detailedCategoryProductCount = $maxCount;
                }
            }

            $formattedStores[] = [
                'name' => $store->store_name,
                'slug' => $store->store_name_slug,
                'description' => \Illuminate\Support\Str::limit($store->store_description, 100),
                'logo' => $store->store_logo ? asset('storage/' . $store->store_logo) : null,
                'product_count' => $seller->products_count,
                'category_product_count' => $detailedCategoryProductCount,
                'user_avatar' => $seller->avatar ? asset('storage/' . $seller->avatar) : null,
                'user_name' => $seller->name,
                'main_category' => $mainDetailedCategory
            ];
        }

        return view('users.browse-stores', compact(
            'cartItemCount',
            'detailedCategories',
            'selectedDetailedCategory',
            'formattedStores',
            'stores' // For pagination
        ));
    }
}
