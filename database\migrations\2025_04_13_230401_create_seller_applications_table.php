<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('seller_applications', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('user_id')->references('id')->on('users')->onUpdate('cascade')->onDelete('cascade');
            $table->string('email');
            $table->string('full_name');
            $table->string('id_type');
            $table->text('id_number'); // Encrypted
            $table->string('id_document')->nullable(); // Path to file on private disk
            $table->text('bank_name'); // Encrypted
            $table->text('account_number'); // Encrypted
            $table->text('account_holder_name'); // Encrypted
            $table->string('payment_method')->default('bank_transfer');
            $table->string('store_name');
            $table->text('store_description');
            // $table->string('store_category');
            $table->string('store_logo')->nullable(); // Path to file on private disk
            $table->enum('status', ['pending', 'approved', 'rejected'])->default('pending');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('seller_applications');
    }
};
