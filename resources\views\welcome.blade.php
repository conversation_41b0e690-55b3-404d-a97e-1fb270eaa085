@extends('layouts.main')

@push('styles')
    <style>
        /* Category card styles */
        .category-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .category-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
    </style>
@endpush

@section('content')
    <!-- Enhanced SEO Metadata -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "<PERSON>gitora",
        "url": "{{ url('/') }}",
        "logo": "{{ asset('images/logo.png') }}",
        "description": "Premier digital marketplace for creators and entrepreneurs to buy and sell high-quality digital products worldwide.",
        "sameAs": [
            "https://twitter.com/digitora",
            "https://facebook.com/digitora",
            "https://instagram.com/digitora",
            "https://linkedin.com/company/digitora"
        ]
    }
    </script>
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "url": "{{ url('/') }}",
        "name": "Digitora - Premier Digital Marketplace",
        "description": "Discover and sell high-quality digital products including eBooks, templates, software, and creative resources.",
        "potentialAction": {
            "@type": "SearchAction",
            "target": "{{ route('user.browse') }}?q={search_term_string}",
            "query-input": "required name=search_term_string"
        }
    }
    </script>
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-r from-purple-50 to-indigo-50 py-20 md:py-20 overflow-hidden">
        <div class="absolute inset-0 opacity-30">
            <div class="circle circle-1"></div>
            <div class="circle circle-2"></div>
            <div class="circle circle-3"></div>
        </div>
        <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="text-center max-w-4xl mx-auto hero-text">
                <span
                    class="inline-block bg-purple-100 text-purple-700 text-sm px-5 py-2 rounded-full mb-6 font-medium animate-fade-in">Premier
                    Digital Marketplace for Creators</span>
                <h1
                    class="text-4xl sm:text-5xl md:text-6xl font-extrabold text-gray-900 mb-6 leading-tight animate-fade-in-up">
                    <span class="block">Discover & Sell High-Quality</span>
                    <span class="bg-gradient-to-r from-purple-600 to-indigo-600 bg-clip-text text-transparent">Digital
                        Products</span>
                </h1>
                <p class="text-lg md:text-xl text-gray-600 mb-10 max-w-2xl mx-auto animate-fade-in-up animation-delay-200">
                    Unlock a world of premium digital assets including eBooks, templates, software, and creative resources.
                    Join creators and entrepreneurs from across the globe on Digitora's seamless marketplace platform.</p>
                <div
                    class="flex flex-col sm:flex-row justify-center sm:space-x-5 space-y-4 sm:space-y-0 animate-fade-in-up animation-delay-400">
                    <a href="{{ route('user.browse') }}" class="btn-primary flex items-center justify-center space-x-2">
                        <span>Explore Now</span>
                        <svg class="h-5 w-5 transform group-hover:translate-x-1 transition-transform duration-200"
                            fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                        </svg>
                    </a>
                    <a href="{{ route('seller.apply') }}" class="btn-secondary">Start Selling</a>
                </div>
            </div>

            <!-- Stats -->
            <div
                class="grid grid-cols-2 md:grid-cols-4 gap-6 md:gap-8 mt-16 max-w-5xl mx-auto animate-fade-in-up animation-delay-600">
                <div
                    class="bg-white bg-opacity-80 backdrop-filter backdrop-blur-md rounded-xl p-5 text-center shadow-sm hover:shadow-md transition-shadow duration-300">
                    <p class="text-3xl font-bold text-purple-600">{{ number_format($productCount) }}+</p>
                    <p class="text-gray-600 font-medium text-sm mt-1">Digital Products</p>
                </div>
                <div
                    class="bg-white bg-opacity-80 backdrop-filter backdrop-blur-md rounded-xl p-5 text-center shadow-sm hover:shadow-md transition-shadow duration-300">
                    <p class="text-3xl font-bold text-purple-600">{{ number_format($sellerCount) }}+</p>
                    <p class="text-gray-600 font-medium text-sm mt-1">Creators</p>
                </div>
                <div
                    class="bg-white bg-opacity-80 backdrop-filter backdrop-blur-md rounded-xl p-5 text-center shadow-sm hover:shadow-md transition-shadow duration-300">
                    <p class="text-3xl font-bold text-purple-600">{{ number_format($userCount) }}+</p>
                    <p class="text-gray-600 font-medium text-sm mt-1">Customers</p>
                </div>
                <div
                    class="bg-white bg-opacity-80 backdrop-filter backdrop-blur-md rounded-xl p-5 text-center shadow-sm hover:shadow-md transition-shadow duration-300">
                    <p class="text-3xl font-bold text-purple-600">{{ $avgRating }}</p>
                    <p class="text-gray-600 font-medium text-sm mt-1">Average Rating</p>
                </div>
            </div>

            <!-- Trust Badges -->
            <div
                class="flex flex-wrap justify-center gap-6 md:gap-8 mt-12 text-gray-700 animate-fade-in-up animation-delay-800">
                <div class="flex items-center space-x-2">
                    <svg class="h-5 w-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z">
                        </path>
                    </svg>
                    <span class="font-medium text-sm">Secure Payments</span>
                </div>
                <div class="flex items-center space-x-2">
                    <svg class="h-5 w-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span class="font-medium text-sm">Quality Guaranteed</span>
                </div>
                <div class="flex items-center space-x-2">
                    <svg class="h-5 w-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z">
                        </path>
                    </svg>
                    <span class="font-medium text-sm">Global Marketplace</span>
                </div>
                <div class="flex items-center space-x-2">
                    <svg class="h-5 w-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z">
                        </path>
                    </svg>
                    <span class="font-medium text-sm">Trusted by Thousands</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Popular Categories -->
    <section class="py-20">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-extrabold text-gray-900 mb-4 animate-fade-in">Explore Popular
                    Categories</h2>
                <p class="text-gray-600 text-lg max-w-2xl mx-auto animate-fade-in animation-delay-200">Discover premium
                    digital assets across diverse categories - from professional templates and eBooks to software tools and
                    creative resources.</p>
            </div>
            <!-- Categories Grid Layout -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6">
                @foreach ($categories as $category)
                    <a href="{{ route('user.browse', ['category' => $category['slug'] ?? Str::slug($category['name'])]) }}"
                        class="bg-white p-6 rounded-xl border border-gray-100 shadow-sm text-center category-card animate-on-scroll"
                        style="animation-delay: {{ $loop->index * 100 }}ms"
                        aria-label="Explore {{ $category['name'] }} category">
                        <div class="flex justify-center mb-4">
                            {!! $category['icon'] !!}
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ $category['name'] }}</h3>
                        <p class="text-gray-500 text-sm">
                            {{ number_format($category['count'], 0, ',', '.') }} products</p>
                    </a>
                @endforeach
            </div>
        </div>
    </section>

    <!-- Featured Stores -->
    <section class="py-20 bg-gray-50">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-extrabold text-gray-900 mb-4 animate-fade-in">Featured Stores</h2>
                <p class="text-gray-600 text-lg max-w-2xl mx-auto animate-fade-in animation-delay-200">Discover our top
                    creators and their specialized digital product collections.</p>
            </div>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                @foreach ($featuredStores as $store)
                    <article
                        class="bg-white rounded-xl border border-gray-100 overflow-hidden shadow-sm card-hover animate-on-scroll"
                        style="animation-delay: {{ $loop->index * 100 }}ms">
                        <div class="relative">
                            @if ($store['logo'])
                                <img src="{{ asset($store['logo']) }}" alt="{{ $store['name'] }}"
                                    class="w-full h-48 object-cover">
                            @else
                                <div
                                    class="w-full h-48 bg-gradient-to-r from-purple-600 to-indigo-600 flex items-center justify-center">
                                    <span class="text-7xl font-bold text-white">{{ substr($store['name'], 0, 1) }}</span>
                                </div>
                            @endif
                            <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                            <div class="absolute bottom-3 left-3 flex items-center">
                                @if ($store['user_avatar'])
                                    <img src="{{ asset($store['user_avatar']) }}" alt="{{ $store['user_name'] }}"
                                        class="w-10 h-10 rounded-full border-2 border-white mr-2">
                                @else
                                    <div
                                        class="w-10 h-10 rounded-full bg-purple-600 text-white flex items-center justify-center font-bold text-lg border-2 border-white mr-2">
                                        {{ substr($store['user_name'], 0, 1) }}
                                    </div>
                                @endif
                                <h3 class="text-white font-bold text-lg">{{ $store['name'] }}</h3>
                            </div>
                        </div>
                        <div class="p-5">
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-xs px-2 py-1 bg-purple-100 text-purple-700 rounded-full">
                                    {{ $store['main_category']['name'] ?? 'Digital Products' }}
                                </span>
                                <span class="text-gray-500 text-sm">{{ $store['product_count'] }} products</span>
                            </div>
                            <p class="text-gray-600 text-sm mb-4 line-clamp-2">{{ $store['description'] }}</p>
                            <a href="/{{ $store['slug'] }}"
                                class="block text-center bg-purple-600 text-white px-4 py-2 rounded-full hover:bg-purple-700 transition-colors duration-300 font-medium text-sm"
                                aria-label="Visit {{ $store['name'] }}">Visit Store</a>
                        </div>
                    </article>
                @endforeach
            </div>

            <div class="text-center mt-12 animate-fade-in-up animation-delay-400">
                <a href="{{ route('user.browse') }}"
                    class="inline-flex items-center text-purple-600 font-semibold hover:text-purple-700 transition-colors duration-300 group">
                    <span>Explore More</span>
                    <svg class="h-5 w-5 ml-2 transform group-hover:translate-x-1 transition-transform duration-200"
                        fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                    </svg>
                </a>
            </div>
        </div>
    </section>

    <!-- Featured Products -->
    <section class="py-20 bg-white">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-extrabold text-gray-900 mb-4 animate-fade-in">Featured Digital
                    Products
                </h2>
                <p class="text-gray-600 text-lg max-w-2xl mx-auto animate-fade-in animation-delay-200">Discover handpicked
                    premium digital products from top global creators designed to elevate your productivity, creativity, and
                    business growth.</p>
                <div class="flex flex-wrap justify-center gap-3 mt-8">
                    <button
                        class="category-filter bg-purple-100 text-purple-700 px-5 py-2 rounded-full font-medium hover:bg-purple-200 transition-colors duration-300 shadow-sm"
                        data-filter="trending" aria-label="Filter by Trending">Trending</button>
                    <button
                        class="category-filter bg-gray-100 text-gray-700 px-5 py-2 rounded-full font-medium hover:bg-gray-200 transition-colors duration-300 shadow-sm"
                        data-filter="new" aria-label="Filter by New Arrivals">New Arrivals</button>
                    <button
                        class="category-filter bg-gray-100 text-gray-700 px-5 py-2 rounded-full font-medium hover:bg-gray-200 transition-colors duration-300 shadow-sm"
                        data-filter="bestsellers" aria-label="Filter by Bestsellers">Bestsellers</button>
                </div>
            </div>

            <div
                class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 products-grid transition-opacity duration-300">
                <!-- Structured Data for Products -->
                <script type="application/ld+json">
                    {
                        "@context": "https://schema.org",
                        "@type": "ItemList",
                        "itemListElement": [
                            @foreach ($featuredProducts as $index => $product)
                                {
                                    "@type": "ListItem",
                                    "position": {{ $index + 1 }},
                                    "item": {
                                        "@type": "Product",
                                        "name": "{{ $product['name'] }}",
                                        "image": "{{ $product['image'] }}",
                                        "description": "A {{ $product['detailed_category'] ?? $product['subcategory'] ?? $product['category'] }} digital product by {{ $product['creator'] }} on Digitora.",
                                        "offers": {
                                            "@type": "Offer",
                                            "price": {{ $product['price'] }},
                                            "priceCurrency": "IDR",
                                            "availability": "https://schema.org/InStock"
                                        },
                                        "aggregateRating": {
                                            "@type": "AggregateRating",
                                            "ratingValue": {{ $product['rating'] }},
                                            "reviewCount": {{ $product['reviews'] }}
                                        }
                                    }
                                }@if (!$loop->last),@endif
                            @endforeach
                        ]
                    }
                </script>
                @foreach ($featuredProducts as $product)
                    <article
                        class="bg-white rounded-xl border border-gray-100 overflow-hidden shadow-sm card-hover animate-on-scroll"
                        style="animation-delay: {{ $loop->index * 100 }}ms">
                        <div class="relative">
                            <img src="{{ $product['image'] }}"
                                alt="{{ $product['name'] }} by {{ $product['creator'] }}"
                                class="w-full h-48 object-cover">
                            @if ($product['discount_price'])
                                <div
                                    class="absolute top-2 right-2 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded">
                                    {{ $product['discount_percentage'] }}% OFF
                                </div>
                            @endif
                        </div>
                        <div class="p-5">
                            <h3 class="text-lg font-semibold text-gray-900 mb-1 line-clamp-2">{{ $product['name'] }}</h3>
                            <div class="flex justify-between items-center mb-2">
                                <p class="text-gray-500 text-sm">by {{ $product['creator'] }}</p>
                                <span
                                    class="text-xs px-2 py-1 bg-purple-100 text-purple-700 rounded-full">{{ $product['detailed_category'] ?? ($product['subcategory'] ?? $product['category']) }}</span>
                            </div>
                            <div class="flex justify-between items-center mb-4">
                                @if ($product['discount_price'])
                                    <div>
                                        <p class="text-gray-500 text-xs line-through">IDR
                                            {{ number_format($product['price'], 0, ',', '.') }}</p>
                                        <p class="text-purple-600 font-bold">IDR
                                            {{ number_format($product['discount_price'], 0, ',', '.') }}</p>
                                    </div>
                                @else
                                    <p class="text-purple-600 font-bold">IDR
                                        {{ number_format($product['price'], 0, ',', '.') }}</p>
                                @endif
                                <div class="flex items-center">
                                    <span class="text-yellow-400 mr-1">★</span>
                                    <span class="text-gray-600 text-sm">{{ $product['rating'] }}
                                        ({{ $product['reviews'] }})
                                    </span>
                                </div>
                            </div>
                            <a href="{{ route('store.product', ['sellerApplication' => $product['store_name_slug'], 'product' => $product['slug']]) }}"
                                class="block text-center bg-purple-600 text-white px-4 py-2 rounded-full hover:bg-purple-700 transition-colors duration-300 font-medium text-sm"
                                aria-label="View details of {{ $product['name'] }}">View Details</a>
                        </div>
                    </article>
                @endforeach
            </div>

            <div class="text-center mt-12 animate-fade-in-up animation-delay-400">
                <a href="{{ route('user.browse') }}"
                    class="inline-flex items-center text-purple-600 font-semibold hover:text-purple-700 transition-colors duration-300 group">
                    <span>View All Products</span>
                    <svg class="h-5 w-5 ml-2 transform group-hover:translate-x-1 transition-transform duration-200"
                        fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                    </svg>
                </a>
            </div>
        </div>
    </section>

    <!-- How Digitora Works -->
    <section class="py-20">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-extrabold text-gray-900 mb-4 animate-fade-in">How Digitora Works</h2>
                <p class="text-gray-600 text-lg max-w-2xl mx-auto animate-fade-in animation-delay-200">Simple steps to buy
                    or sell premium digital products on our global marketplace platform.</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-5xl mx-auto">
                @foreach ($steps as $step)
                    <div class="text-center animate-on-scroll" style="animation-delay: {{ $step['step'] * 100 }}ms">
                        <div class="flex justify-center mb-5">
                            <span
                                class="bg-purple-100 text-purple-700 text-xl font-semibold rounded-full w-12 h-12 flex items-center justify-center shadow-sm">{{ $step['step'] }}</span>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">{{ $step['title'] }}</h3>
                        <p class="text-gray-600 text-sm leading-relaxed">{{ $step['description'] }}</p>
                    </div>
                @endforeach
            </div>
        </div>
    </section>

    <!-- Testimonials -->
    @if (count($testimonials) > 0)
        <section class="py-20 bg-gradient-to-r from-purple-50 to-indigo-50">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-extrabold text-gray-900 mb-4 animate-fade-in">What Our Community
                        Says</h2>
                    <p class="text-gray-600 text-lg max-w-2xl mx-auto animate-fade-in animation-delay-200">Hear from our
                        global community of creators and customers who trust Digitora for their premium digital product
                        needs.</p>
                </div>

                <div
                    class="grid grid-cols-1 md:grid-cols-{{ count($testimonials) >= 3 ? '3' : (count($testimonials) == 2 ? '2' : '1') }} gap-8 max-w-5xl mx-auto">
                    <!-- Testimonials from real reviews -->
                    @foreach ($testimonials as $testimonial)
                        <div class="bg-white p-6 rounded-xl border border-gray-100 shadow-sm card-hover animate-on-scroll"
                            style="animation-delay: {{ $loop->index * 100 }}ms">
                            <div class="flex items-center mb-3">
                                <div class="flex">
                                    @for ($i = 1; $i <= 5; $i++)
                                        @if ($i <= $testimonial['rating'])
                                            <svg class="h-4 w-4 text-yellow-400" xmlns="http://www.w3.org/2000/svg"
                                                viewBox="0 0 20 20" fill="currentColor">
                                                <path
                                                    d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                            </svg>
                                        @else
                                            <svg class="h-4 w-4 text-gray-300" xmlns="http://www.w3.org/2000/svg"
                                                viewBox="0 0 20 20" fill="currentColor">
                                                <path
                                                    d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                            </svg>
                                        @endif
                                    @endfor
                                </div>
                            </div>
                            <p class="text-gray-600 italic mb-5 text-lg leading-relaxed">"{{ $testimonial['quote'] }}"</p>
                            <div class="flex items-center">
                                <div
                                    class="bg-purple-100 rounded-full w-12 h-12 flex items-center justify-center text-purple-700 font-bold text-xl mr-3">
                                    {{ substr($testimonial['author'], 0, 1) }}
                                </div>
                                <div>
                                    <p class="text-gray-900 font-semibold">{{ $testimonial['author'] }}</p>
                                    <p class="text-gray-500 text-sm">{{ $testimonial['role'] }}</p>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </section>
    @endif

    <!-- Final CTA -->
    <section class="py-20 bg-gradient-to-r from-purple-600 to-indigo-600 text-white text-center">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl md:text-4xl font-extrabold mb-4 animate-fade-in">Ready to Join Digitora?</h2>
            <p class="text-lg mb-8 max-w-2xl mx-auto animate-fade-in animation-delay-200">Be part of a thriving global
                community of creators and customers on our leading digital marketplace.</p>
            <div
                class="flex flex-col sm:flex-row justify-center sm:space-x-5 space-y-4 sm:space-y-0 animate-fade-in-up animation-delay-400">
                <a href="{{ route('seller.apply') }}"
                    class="bg-white text-purple-600 px-8 py-3 rounded-full font-medium hover:bg-gray-100 transition-all duration-300 shadow-sm hover:shadow">Start
                    Selling Today</a>
                <a href="{{ route('user.browse') }}"
                    class="border-2 border-white text-white px-8 py-3 rounded-full font-medium hover:bg-white hover:text-purple-600 transition-all duration-300">Explore
                    Products Now</a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-white py-12 border-t">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <a href="{{ route('home') }}" class="flex items-center space-x-3 mb-4"
                        aria-label="Digitora Homepage">
                        <div
                            class="bg-gradient-to-r from-purple-600 to-indigo-600 text-white w-10 h-10 rounded-lg flex items-center justify-center text-2xl font-bold">
                            D</div>
                        <span class="text-2xl font-extrabold text-gray-900 tracking-tight">Digitora</span>
                    </a>
                    <p class="text-gray-600 text-sm leading-relaxed">Empowering creators and entrepreneurs worldwide with a
                        seamless platform to buy and sell high-quality digital products.</p>
                </div>
                {{-- <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Links</h3>
                    <ul class="space-y-3 text-gray-600 text-sm">
                        <li><a href="{{ route('user.browse') }}"
                                class="hover:text-purple-600 transition-colors duration-200"
                                aria-label="Browse Digital Products">Browse Products</a></li>
                        <li><a href="{{ route('seller.apply') }}"
                                class="hover:text-purple-600 transition-colors duration-200"
                                aria-label="Become a Seller">Become a Seller</a></li>
                        <li><a href="{{ route('pricing') }}" class="hover:text-purple-600 transition-colors duration-200"
                                aria-label="Pricing Information">Pricing</a></li>
                        <li><a href="{{ route('success-stories') }}"
                                class="hover:text-purple-600 transition-colors duration-200"
                                aria-label="Success Stories">Success Stories</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Company</h3>
                    <ul class="space-y-3 text-gray-600 text-sm">
                        <li><a href="{{ route('about') }}" class="hover:text-purple-600 transition-colors duration-200"
                                aria-label="About Digitora">About Us</a></li>
                        <li><a href="{{ route('blog') }}" class="hover:text-purple-600 transition-colors duration-200"
                                aria-label="Digitora Blog">Blog</a></li>
                        <li><a href="{{ route('careers') }}" class="hover:text-purple-600 transition-colors duration-200"
                                aria-label="Careers at Digitora">Careers</a></li>
                        <li><a href="{{ route('contact') }}" class="hover:text-purple-600 transition-colors duration-200"
                                aria-label="Contact Digitora">Contact Us</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Support</h3>
                    <ul class="space-y-3 text-gray-600 text-sm">
                        <li><a href="{{ route('help') }}" class="hover:text-purple-600 transition-colors duration-200"
                                aria-label="Help Center">Help Center</a></li>
                        <li><a href="{{ route('terms.alt') }}" class="hover:text-purple-600 transition-colors duration-200"
                                aria-label="Terms of Service" target="_blank">Terms of Service</a></li>
                        <li><a href="{{ route('privacy.alt') }}" class="hover:text-purple-600 transition-colors duration-200"
                                aria-label="Privacy Policy" target="_blank">Privacy Policy</a></li>
                        <li><a href="{{ route('faq') }}" class="hover:text-purple-600 transition-colors duration-200"
                                aria-label="Frequently Asked Questions">FAQ</a></li>
                    </ul>
                </div> --}}

                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Links</h3>
                    <ul class="space-y-3 text-gray-600 text-sm">
                        <li><a href="{{ route('user.browse') }}"
                                class="hover:text-purple-600 transition-colors duration-200"
                                aria-label="Browse Digital Products">Browse Products</a></li>
                        <li><a href="{{ route('seller.apply') }}"
                                class="hover:text-purple-600 transition-colors duration-200"
                                aria-label="Become a Seller">Become a Seller</a></li>
                        <li><a href="/" class="hover:text-purple-600 transition-colors duration-200"
                                aria-label="Pricing Information">Pricing</a></li>
                        <li><a href="/" class="hover:text-purple-600 transition-colors duration-200"
                                aria-label="Success Stories">Success Stories</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Company</h3>
                    <ul class="space-y-3 text-gray-600 text-sm">
                        <li><a href="/" class="hover:text-purple-600 transition-colors duration-200"
                                aria-label="About Digitora">About Us</a></li>
                        <li><a href="/" class="hover:text-purple-600 transition-colors duration-200"
                                aria-label="Digitora Blog">Blog</a></li>
                        <li><a href="/" class="hover:text-purple-600 transition-colors duration-200"
                                aria-label="Careers at Digitora">Careers</a></li>
                        <li><a href="/" class="hover:text-purple-600 transition-colors duration-200"
                                aria-label="Contact Digitora">Contact Us</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Support</h3>
                    <ul class="space-y-3 text-gray-600 text-sm">
                        <li><a href="/" class="hover:text-purple-600 transition-colors duration-200"
                                aria-label="Help Center">Help Center</a></li>
                        <li><a href="{{ route('terms.alt') }}"
                                class="hover:text-purple-600 transition-colors duration-200" aria-label="Terms of Service"
                                target="_blank">Terms of Service</a></li>
                        <li><a href="{{ route('privacy.alt') }}"
                                class="hover:text-purple-600 transition-colors duration-200" aria-label="Privacy Policy"
                                target="_blank">Privacy Policy</a></li>
                        <li><a href="/" class="hover:text-purple-600 transition-colors duration-200"
                                aria-label="Frequently Asked Questions">FAQ</a></li>
                    </ul>
                </div>
            </div>
        </div>


        <div class="mt-12 text-center text-gray-600 text-sm">
            <p>&copy; {{ date('Y') }} Digitora. All rights reserved.</p>
        </div>
        </div>
    </footer>
@endsection

@push('scripts')
    <!-- All scripts moved to public/dev-js/digitora.js -->
@endpush
