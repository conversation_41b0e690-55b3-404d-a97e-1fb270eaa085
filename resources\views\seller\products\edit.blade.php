@extends('seller.layouts.app')

@push('scripts')
    <script>
        // Pass the category mappings from the server to JavaScript
        const legacyCategoryMapping = @json($legacyCategoryMapping ?? []);
        const legacySubcategoryMapping = @json($legacySubcategoryMapping ?? []);
    </script>
    <script src="{{ asset(js_path() . '/category-selector.js') }}"></script>
@endpush

@section('content')
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <div class="space-y-6">
        <div class="flex items-center gap-4">
            <a href="{{ route('seller.products.index', [], false) ?? '#' }}"
                class="rounded-lg p-2 text-gray-500 hover:bg-gray-100 hover:text-gray-600 transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5">
                    <path d="m15 18-6-6 6-6"></path>
                </svg>
                <span class="sr-only">Back</span>
            </a>
            <div>
                <h1 class="text-3xl font-bold tracking-tight text-gray-900">Edit Product</h1>
                <p class="text-gray-600">Update your digital product details</p>
            </div>
        </div>

        <form id="product-form" action="{{ route('seller.products.update', $product, false) ?? '#' }}" method="POST"
            enctype="multipart/form-data" onsubmit="if(typeof tinyMCE !== 'undefined') { tinyMCE.triggerSave(); }">
            @csrf
            @method('PUT')
            <div class="grid gap-6 md:grid-cols-6">
                <div class="space-y-6 md:col-span-4">
                    <div class="rounded-xl border bg-white shadow-lg">
                        <div class="border-b border-gray-100 p-6">
                            <h3 class="text-lg font-semibold text-gray-900">Product Information</h3>
                            <p class="text-sm text-gray-600">Basic information about your digital product</p>
                        </div>
                        <div class="p-6 space-y-5">
                            <div class="space-y-2">
                                <label for="name" class="block text-sm font-medium text-gray-700">
                                    Product Name <span class="text-red-500">*</span>
                                </label>
                                <input type="text" name="name" id="name"
                                    class="block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors"
                                    placeholder="e.g. Financial Planning Spreadsheet"
                                    value="{{ old('name', $product->name) }}" required>
                                @error('name')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                            <div class="space-y-2">
                                <label for="description" class="block text-sm font-medium text-gray-700">
                                    Description <span class="text-red-500">*</span>
                                </label>
                                <textarea name="description" id="description" rows="6"
                                    class="block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors"
                                    placeholder="Describe your product in detail..." required>{{ old('description', $product->description) }}</textarea>
                                <p class="text-xs text-gray-500">
                                    Provide a detailed description of your product, including its features and benefits.
                                </p>
                                @error('description')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                            <div class="space-y-2">
                                <label for="category" class="block text-sm font-medium text-gray-700">
                                    Legacy Category <span class="text-red-500">*</span>
                                </label>
                                <select name="category" id="category"
                                    class="block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors"
                                    required>
                                    <option value="">Select a category</option>
                                    @foreach ($categoryGroups ?? [] as $groupName => $categories)
                                        <optgroup label="{{ $groupName }}">
                                            @foreach ($categories as $value => $label)
                                                <option value="{{ $value }}"
                                                    {{ old('category', $product->category) == $value ? 'selected' : '' }}>
                                                    {{ $label }}</option>
                                            @endforeach
                                        </optgroup>
                                    @endforeach
                                </select>
                                @error('category')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="space-y-2">
                                <label for="category_id" class="block text-sm font-medium text-gray-700">
                                    Detailed Category <span class="text-red-500">*</span>
                                </label>
                                <div class="relative">
                                    <input type="text" id="category_search"
                                        class="block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors"
                                        placeholder="Search for a category..." autocomplete="off">
                                    <div id="category_dropdown"
                                        class="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md border border-gray-200 hidden max-h-60 overflow-y-auto">
                                        <div class="p-2">
                                            @foreach ($categoryTree ?? [] as $topCategory)
                                                <div class="category-group mb-2">
                                                    <div class="font-medium text-gray-700 py-1">{{ $topCategory->name }}
                                                    </div>
                                                    @foreach ($topCategory->activeSubcategories as $subCategory)
                                                        <div class="pl-3 mb-1">
                                                            <div class="font-medium text-gray-600 py-1">
                                                                {{ $subCategory->name }}</div>
                                                            @foreach ($subCategory->activeDetailedCategories as $detailedCategory)
                                                                <div class="pl-3">
                                                                    <div class="category-option py-1 px-2 hover:bg-gray-100 rounded cursor-pointer"
                                                                        data-id="{{ $detailedCategory->id }}"
                                                                        data-category-id="{{ $topCategory->id }}"
                                                                        data-subcategory-id="{{ $subCategory->id }}"
                                                                        data-name="{{ $topCategory->name }} > {{ $subCategory->name }} > {{ $detailedCategory->name }}">
                                                                        {{ $detailedCategory->name }}
                                                                    </div>
                                                                </div>
                                                            @endforeach
                                                        </div>
                                                    @endforeach
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                                <input type="hidden" name="category_id" id="category_id"
                                    value="{{ old('category_id', $product->category_id) }}">
                                <input type="hidden" name="subcategory_id" id="subcategory_id"
                                    value="{{ old('subcategory_id', $product->subcategory_id) }}">
                                <input type="hidden" name="detailed_category_id" id="detailed_category_id"
                                    value="{{ old('detailed_category_id', $product->detailed_category_id) }}">
                                <div id="selected_category"
                                    class="mt-2 p-2 bg-gray-50 rounded-md {{ old('detailed_category_id', $product->detailed_category_id) ? '' : 'hidden' }}">
                                    <div class="flex items-center justify-between">
                                        <span id="selected_category_name" class="text-sm text-gray-700">
                                            @if ($product->productDetailedCategory)
                                                {{ $product->categoryPath }}
                                            @endif
                                        </span>
                                        <button type="button" id="clear_category"
                                            class="text-sm text-red-500 hover:text-red-700">
                                            Clear
                                        </button>
                                    </div>
                                </div>
                                <p class="text-xs text-gray-500">
                                    Select the most specific category for your product to help buyers find it.
                                </p>
                                @error('category_id')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="rounded-xl border bg-white shadow-lg">
                        <div class="border-b border-gray-100 p-6">
                            <h3 class="text-lg font-semibold text-gray-900">Product Images <span
                                    class="text-red-500">*</span></h3>
                            <p class="text-sm text-gray-600">Update the images for your product (required when publishing).
                                The first image will be used as the cover.</p>
                        </div>
                        <div class="p-6 space-y-5">
                            @if ($product->images->count() > 0)
                                <div class="space-y-2">
                                    <div class="flex justify-between items-center">
                                        <label class="block text-sm font-medium text-gray-700">Current Images</label>
                                        <p class="text-xs text-gray-500">Drag to reorder. First image is the cover.</p>
                                    </div>
                                    <div id="sortable-images" class="space-y-2">
                                        @foreach ($product->images as $image)
                                            <div class="flex items-center justify-between rounded-lg border border-gray-200 p-3 existing-image-item"
                                                data-image-id="{{ $image->id }}"
                                                data-sort-order="{{ $image->sort_order }}">
                                                <div class="flex items-center gap-3">
                                                    <div class="cursor-move handle">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="16"
                                                            height="16" viewBox="0 0 24 24" fill="none"
                                                            stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                            stroke-linejoin="round" class="h-5 w-5 text-gray-400">
                                                            <line x1="21" y1="10" x2="3"
                                                                y2="10"></line>
                                                            <line x1="21" y1="6" x2="3"
                                                                y2="6"></line>
                                                            <line x1="21" y1="14" x2="3"
                                                                y2="14"></line>
                                                            <line x1="21" y1="18" x2="3"
                                                                y2="18"></line>
                                                        </svg>
                                                    </div>
                                                    <img src="{{ asset('storage/' . $image->path) }}"
                                                        alt="{{ $product->name }}"
                                                        class="h-12 w-12 rounded-lg object-cover shadow-sm">
                                                    <div>
                                                        <p class="text-sm font-medium text-gray-900 image-label">
                                                            {{ $image->is_primary ? '📌 Cover Image' : 'Product Image' }}
                                                        </p>
                                                        <p class="text-xs text-gray-500">Uploaded previously</p>
                                                    </div>
                                                </div>
                                                <div class="flex items-center gap-2">
                                                    <button type="button"
                                                        class="set-primary-image rounded-lg p-2 text-gray-500 hover:bg-gray-100 hover:text-indigo-600 transition-colors {{ $image->is_primary ? 'hidden' : '' }}"
                                                        data-image-id="{{ $image->id }}">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="16"
                                                            height="16" viewBox="0 0 24 24" fill="none"
                                                            stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                            stroke-linejoin="round" class="h-5 w-5">
                                                            <path
                                                                d="M12 2L15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2z">
                                                            </path>
                                                        </svg>
                                                    </button>
                                                    <button type="button"
                                                        class="remove-existing-image rounded-lg p-2 text-gray-500 hover:bg-gray-100 hover:text-red-600 transition-colors"
                                                        data-image-id="{{ $image->id }}">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="16"
                                                            height="16" viewBox="0 0 24 24" fill="none"
                                                            stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                            stroke-linejoin="round" class="h-5 w-5">
                                                            <path d="M3 6h18"></path>
                                                            <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                                                            <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                                                            <line x1="10" y1="11" x2="10"
                                                                y2="17"></line>
                                                            <line x1="14" y1="11" x2="14"
                                                                y2="17"></line>
                                                        </svg>
                                                    </button>
                                                </div>
                                                <input type="hidden" name="image_order[]" value="{{ $image->id }}">
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @elseif($product->image)
                                <div class="space-y-2">
                                    <label class="block text-sm font-medium text-gray-700">Current Image</label>
                                    <div
                                        class="flex items-center justify-between rounded-lg border border-gray-200 p-3 existing-image">
                                        <div class="flex items-center gap-3">
                                            <img src="{{ asset('storage/' . $product->image) }}"
                                                alt="{{ $product->name }}"
                                                class="h-12 w-12 rounded-lg object-cover shadow-sm">
                                            <div>
                                                <p class="text-sm font-medium text-gray-900">Current Image</p>
                                                <p class="text-xs text-gray-500">Uploaded previously</p>
                                            </div>
                                        </div>
                                        <button type="button"
                                            class="remove-image rounded-lg p-2 text-gray-500 hover:bg-gray-100 hover:text-gray-600 transition-colors">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                class="h-5 w-5">
                                                <path d="M3 6h18"></path>
                                                <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                                                <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                                                <line x1="10" y1="11" x2="10" y2="17">
                                                </line>
                                                <line x1="14" y1="11" x2="14" y2="17">
                                                </line>
                                            </svg>
                                        </button>
                                        <input type="hidden" name="remove_image" class="remove-image-input"
                                            value="">
                                    </div>
                                </div>
                            @endif

                            <div class="rounded-lg border-2 border-dashed border-gray-200 p-8 upload-area">
                                <div class="flex flex-col items-center justify-center gap-3 text-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                        stroke-linecap="round" stroke-linejoin="round" class="h-10 w-10 text-gray-400">
                                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                        <polyline points="17 8 12 3 7 8"></polyline>
                                        <line x1="12" y1="3" x2="12" y2="15"></line>
                                    </svg>
                                    <p class="text-sm font-medium text-gray-700">Drag and drop images here or click to
                                        browse</p>
                                    <p class="text-xs text-gray-500">
                                        Upload product images (JPEG, PNG, JPG, GIF, max 2MB each, up to 10 images)
                                    </p>
                                    <input id="images" name="images[]" type="file" class="hidden"
                                        accept="image/jpeg,image/png,image/jpg,image/gif" multiple>
                                    <button type="button"
                                        class="choose-images mt-2 inline-flex items-center rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 transition-colors">
                                        Choose Images
                                    </button>
                                </div>
                            </div>
                            <div id="images-preview" class="space-y-2 hidden">
                                <label class="block text-sm font-medium text-gray-700">Newly Uploaded Images</label>
                                <div id="images-list" class="space-y-2"></div>
                            </div>
                            @error('images')
                                <div class="mt-3 p-3 rounded-lg bg-red-50 border border-red-200">
                                    <div class="flex items-center gap-2 text-red-600">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5">
                                            <circle cx="12" cy="12" r="10"></circle>
                                            <line x1="12" y1="8" x2="12" y2="12"></line>
                                            <line x1="12" y1="16" x2="12.01" y2="16"></line>
                                        </svg>
                                        <span class="text-sm font-medium">{{ $message }}</span>
                                    </div>
                                </div>
                            @enderror
                            @error('images.*')
                                <div class="mt-3 p-3 rounded-lg bg-red-50 border border-red-200">
                                    <div class="flex items-center gap-2 text-red-600">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5">
                                            <circle cx="12" cy="12" r="10"></circle>
                                            <line x1="12" y1="8" x2="12" y2="12"></line>
                                            <line x1="12" y1="16" x2="12.01" y2="16"></line>
                                        </svg>
                                        <span class="text-sm font-medium">{{ $message }}</span>
                                    </div>
                                </div>
                            @enderror
                        </div>
                    </div>

                    <div class="rounded-xl border bg-white shadow-lg">
                        <div class="border-b border-gray-100 p-6">
                            <h3 class="text-lg font-semibold text-gray-900">Product Files <span
                                    class="text-red-500">*</span></h3>
                            <p class="text-sm text-gray-600">Manage the files that customers will receive (required when
                                publishing)</p>
                        </div>
                        <div class="p-6 space-y-5">
                            @php
                                $existingFiles = json_decode($product->files, true) ?: [];
                            @endphp
                            @if (!empty($existingFiles))
                                <div class="space-y-2">
                                    <label class="block text-sm font-medium text-gray-700">Existing Files</label>
                                    <div id="existing-file-list" class="space-y-2">
                                        @foreach ($existingFiles as $index => $file)
                                            <div class="flex items-center justify-between rounded-lg border border-gray-200 p-3 existing-file-item"
                                                data-index="{{ $index }}">
                                                <div class="flex items-center gap-3">
                                                    <div
                                                        class="flex h-12 w-12 items-center justify-center rounded-lg bg-gray-100">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="16"
                                                            height="16" viewBox="0 0 24 24" fill="none"
                                                            stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                            stroke-linejoin="round" class="h-6 w-6 text-gray-500">
                                                            <path
                                                                d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z">
                                                            </path>
                                                            <polyline points="14 2 14 8 20 8"></polyline>
                                                            <line x1="16" y1="13" x2="8"
                                                                y2="13"></line>
                                                            <line x1="16" y1="17" x2="8"
                                                                y2="17"></line>
                                                            <line x1="10" y1="9" x2="8"
                                                                y2="9"></line>
                                                        </svg>
                                                    </div>
                                                    <div>
                                                        <p class="text-sm font-medium text-gray-900">{{ $file['name'] }}
                                                        </p>
                                                        <p class="text-xs text-gray-500">
                                                            {{ number_format($file['size'] / 1024 / 1024, 2) }} MB</p>
                                                    </div>
                                                </div>
                                                <button type="button"
                                                    class="remove-existing-file rounded-lg p-2 text-gray-500 hover:bg-gray-100 hover:text-gray-600 transition-colors"
                                                    data-index="{{ $index }}">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                        class="h-5 w-5">
                                                        <path d="M3 6h18"></path>
                                                        <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                                                        <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                                                        <line x1="10" y1="11" x2="10"
                                                            y2="17"></line>
                                                        <line x1="14" y1="11" x2="14"
                                                            y2="17"></line>
                                                    </svg>
                                                </button>
                                                <input type="hidden" name="remove_files[]" class="remove-file-input"
                                                    value="" data-index="{{ $index }}">
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endif

                            <div class="rounded-lg border-2 border-dashed border-gray-200 p-8 upload-area">
                                <div class="flex flex-col items-center justify-center gap-3 text-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                        stroke-linecap="round" stroke-linejoin="round" class="h-10 w-10 text-gray-400">
                                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                        <polyline points="17 8 12 3 7 8"></polyline>
                                        <line x1="12" y1="3" x2="12" y2="15"></line>
                                    </svg>
                                    <p class="text-sm font-medium text-gray-700">Drag and drop files here or click to
                                        browse</p>
                                    <p class="text-xs text-gray-500">
                                        Upload additional files that customers will download after purchase (ZIP, PDF, DOCX,
                                        XLSX, max 20MB each, up to 5 files)
                                    </p>
                                    <input id="files" name="files[]" type="file" class="hidden" multiple
                                        accept=".zip,.pdf,.docx,.xlsx">
                                    <button type="button"
                                        class="choose-files mt-2 inline-flex items-center rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 transition-colors">
                                        Choose Files
                                    </button>
                                </div>
                            </div>
                            <div id="file-preview" class="space-y-2 hidden">
                                <label class="block text-sm font-medium text-gray-700">Newly Uploaded Files</label>
                                <div id="file-list" class="space-y-2"></div>
                            </div>
                            @error('files')
                                <div class="mt-3 p-3 rounded-lg bg-red-50 border border-red-200">
                                    <div class="flex items-center gap-2 text-red-600">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5">
                                            <circle cx="12" cy="12" r="10"></circle>
                                            <line x1="12" y1="8" x2="12" y2="12"></line>
                                            <line x1="12" y1="16" x2="12.01" y2="16"></line>
                                        </svg>
                                        <span class="text-sm font-medium">{{ $message }}</span>
                                    </div>
                                </div>
                            @enderror
                            @error('files.*')
                                <div class="mt-3 p-3 rounded-lg bg-red-50 border border-red-200">
                                    <div class="flex items-center gap-2 text-red-600">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5">
                                            <circle cx="12" cy="12" r="10"></circle>
                                            <line x1="12" y1="8" x2="12" y2="12"></line>
                                            <line x1="12" y1="16" x2="12.01" y2="16"></line>
                                        </svg>
                                        <span class="text-sm font-medium">{{ $message }}</span>
                                    </div>
                                </div>
                            @enderror
                        </div>
                    </div>
                </div>

                <div class="space-y-6 md:col-span-2">
                    <div class="rounded-xl border bg-white shadow-lg">
                        <div class="border-b border-gray-100 p-6">
                            <h3 class="text-lg font-semibold text-gray-900">Pricing</h3>
                            <p class="text-sm text-gray-600">Set your product pricing</p>
                        </div>
                        <div class="p-6 space-y-5">
                            <div class="space-y-2">
                                <label for="price" class="block text-sm font-medium text-gray-700">
                                    Price (Rp) <span class="text-red-500">*</span>
                                </label>
                                <input type="number" name="price" id="price" step="0.01" min="5000"
                                    class="block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors"
                                    placeholder="29999" value="{{ old('price', $product->price) }}" required>
                                <p class="mt-1 text-xs text-gray-500">Minimum price is Rp 5,000</p>
                                @error('price')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                            <div class="flex items-center space-x-3">
                                <input type="checkbox" name="has_discount" id="has_discount"
                                    class="rounded border-gray-200 text-indigo-600 focus:ring-indigo-500">
                                <label for="has_discount" class="text-sm font-medium text-gray-700">Enable discount
                                    price</label>
                            </div>
                            <div id="discount-price-container"
                                class="space-y-2 {{ old('has_discount', $product->discount_price) ? '' : 'hidden' }}">
                                <label for="discount_price" class="block text-sm font-medium text-gray-700">Discount Price
                                    (Rp)</label>
                                <input type="number" name="discount_price" id="discount_price" step="0.01"
                                    min="5000"
                                    class="block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors"
                                    placeholder="19999" value="{{ old('discount_price', $product->discount_price) }}">
                                <p class="mt-1 text-xs text-gray-500">Minimum discount price is Rp 5,000</p>
                                @error('discount_price')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="rounded-xl border bg-white shadow-lg">
                        <div class="border-b border-gray-100 p-6">
                            <h3 class="text-lg font-semibold text-gray-900">Publishing</h3>
                            <p class="text-sm text-gray-600">Control your product visibility</p>
                        </div>
                        <div class="p-6 space-y-5">
                            <div x-data="{ status: '{{ old('status', $product->status) }}' }">
                                <div class="space-y-2">
                                    <label class="block text-sm font-medium text-gray-700">Status</label>
                                    <select name="status" x-model="status"
                                        class="block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors">
                                        <option value="draft">Draft</option>
                                        <option value="active">Published</option>
                                    </select>
                                </div>
                                <div x-show="status === 'draft'" class="mt-4">
                                    <p class="text-sm text-gray-500">
                                        Save as draft to continue working on it later. It won't be visible to customers.
                                    </p>
                                </div>
                                <div x-show="status === 'active'" class="mt-4">
                                    <p class="text-sm text-gray-500">
                                        Publish your product to make it available for purchase immediately.
                                    </p>
                                </div>
                                <div class="flex flex-col gap-3 pt-4">
                                    <button type="submit"
                                        :class="{
                                            'bg-amber-600 hover:bg-amber-700': status === 'draft',
                                            'bg-emerald-600 hover:bg-emerald-700': status === 'active'
                                        }"
                                        class="inline-flex w-full items-center justify-center rounded-lg px-4 py-2 text-sm font-medium text-white shadow-md transition-colors">
                                        <span
                                            x-text="status === 'active' ? 'Update and Publish' : 'Update as Draft'"></span>
                                    </button>
                                    <a href="{{ route('seller.products.index', [], false) ?? '#' }}"
                                        class="inline-flex w-full items-center justify-center rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 transition-colors">
                                        Cancel
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    @push('scripts')
        {{-- load tinymce --}}
        <script src="{{ asset('tinymce/tinymce.min.js') }}"></script>

        <script>
            tinymce.init({
                selector: "textarea#description",
                plugins: "autolink lists table lists",
                toolbar: "a11ycheck addcomment showcomments casechange checklist code export formatpainter pageembed permanentpen table tableofcontents numlist bullist",
                toolbar_mode: "floating",
                tinycomments_mode: "embedded",
                tinycomments_author: "Author name",
                setup: function(editor) {
                    // Update the hidden textarea on change
                    editor.on('change', function() {
                        editor.save(); // This will update the textarea with the content
                    });

                    // Handle form submission
                    editor.on('submit', function() {
                        editor.save();
                    });
                }
            });
        </script>
    @endpush
@endsection
