<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Database\Seeder;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run()
    {
        User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('Adm!n2024#SecurePlatform'),
            'is_admin' => true,
        ]);

        User::create([
            'name' => 'renata',
            'email' => '<EMAIL>',
            'password' => Hash::make('Ren@ta2024#Secure!'),
            'is_admin' => true,
            'is_superadmin' => true,
            'is_seller' => true,
        ]);

        User::create([
            'name' => 'Regular User',
            'email' => '<EMAIL>',
            'password' => Hash::make('Customer@2024#A!'),
            'is_admin' => false,
        ]);

        User::create([
            'name' => 'Regular User',
            'email' => '<EMAIL>',
            'password' => Hash::make('Customer@2024#B!'),
            'is_admin' => false,
        ]);

        User::create([
            'name' => 'Regular User',
            'email' => '<EMAIL>',
            'password' => Hash::make('Customer@2024#C!'),
            'is_admin' => false,
        ]);

        User::create([
            'name' => 'Regular User',
            'email' => '<EMAIL>',
            'password' => Hash::make('Customer@2024#D!'),
            'is_admin' => false,
        ]);


    }
}
