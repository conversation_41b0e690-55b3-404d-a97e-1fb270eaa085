@extends('store.layout')

@section('content')
    <!-- Store Hero Section -->
    <div class="hero-gradient py-16 md:py-20">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8 md:gap-12 items-center">
                <div class="animate-on-scroll">
                    <h1 class="text-3xl md:text-4xl lg:text-5xl font-bold text-white leading-tight">Welcome to <span class="text-white">{{ $seller->store_name }}</span></h1>
                    <p class="mt-4 md:mt-6 text-lg md:text-xl text-indigo-100 leading-relaxed">
                        {{ $seller->store_description ?? 'Discover our collection of premium digital products designed to help you succeed.' }}
                    </p>
                    <div class="mt-8 md:mt-10 flex flex-col sm:flex-row gap-4">
                        <a href="#products"
                            class="inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-lg shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 transition-colors duration-300">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7" />
                            </svg>
                            Browse Products
                        </a>
                        <a href="{{ route('store.about', $seller->store_slug) }}"
                            class="inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-lg shadow-sm text-indigo-700 bg-white hover:bg-indigo-50 transition-colors duration-300">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            Learn More
                        </a>
                    </div>
                </div>
                <div class="hidden md:block animate-on-scroll" style="animation-delay: 200ms;">
                    <div class="relative flex justify-center">
                        <div class="absolute -inset-0.5 bg-white rounded-lg blur opacity-30"></div>
                        <div class="relative bg-white/10 backdrop-blur-sm p-8 rounded-lg shadow-xl border border-white/20 flex items-center justify-center">
                            @if ($seller->store_logo)
                                <img src="{{ route('store.logo', $seller->store_slug) }}" alt="{{ $seller->store_name }}"
                                    class="h-48 w-48 rounded-lg object-cover shadow-lg">
                            @else
                                <div class="flex h-48 w-48 items-center justify-center rounded-lg hero-gradient text-white shadow-lg">
                                    <span class="text-7xl font-bold">{{ substr($seller->store_name, 0, 1) }}</span>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Featured Products Section -->
    @if (isset($featuredProducts) && $featuredProducts->count() > 0)
        <div class="py-12 bg-white">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-10 animate-on-scroll">
                    <h2 class="text-2xl md:text-3xl font-bold text-gray-900">Featured Products</h2>
                    <p class="mt-3 text-lg text-gray-600 max-w-3xl mx-auto">
                        Explore our most popular digital products
                    </p>
                </div>

                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 md:gap-8">
                    @foreach ($featuredProducts as $product)
                    <div class="group relative animate-on-scroll" style="animation-delay: {{ $loop->index * 100 }}ms">
                        <div class="aspect-w-4 aspect-h-3 rounded-lg overflow-hidden bg-gray-100">
                            <img src="{{ $product->image ? asset('storage/' . $product->image) : asset('images/placeholder.jpg') }}" alt="{{ $product->name }}" class="w-full h-full object-center object-cover group-hover:opacity-75">
                            @if ($product->discount_price)
                            <div class="absolute top-2 right-2 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded">
                                SALE
                            </div>
                            @endif
                        </div>
                        <div class="mt-4 flex items-center justify-between">
                            <div>
                                <h3 class="text-sm font-medium text-gray-900">
                                    <a href="{{ route('store.product', [$seller->store_slug, $product->slug]) }}">
                                        <span aria-hidden="true" class="absolute inset-0"></span>
                                        {{ $product->name }}
                                    </a>
                                </h3>
                                <p class="mt-1 text-sm text-gray-500">
                                    @if(isset($hasNewCategories) && $hasNewCategories && $product->productDetailedCategory)
                                        {{ $product->productDetailedCategory->name }}
                                    @elseif(isset($hasNewCategories) && $hasNewCategories && $product->productSubcategory)
                                        {{ $product->productSubcategory->name }}
                                    @elseif(isset($hasNewCategories) && $hasNewCategories && $product->productCategory)
                                        {{ $product->productCategory->name }}
                                    @else
                                        {{ ucfirst($product->category) }}
                                    @endif
                                </p>
                            </div>
                            <div>
                                @if ($product->discount_price)
                                    <p class="text-sm font-medium text-indigo-600">Rp {{ number_format($product->discount_price, 0, ',', '.') }}</p>
                                    <p class="text-xs text-gray-500 line-through">Rp {{ number_format($product->price, 0, ',', '.') }}</p>
                                @else
                                    <p class="text-sm font-medium text-indigo-600">Rp {{ number_format($product->price, 0, ',', '.') }}</p>
                                @endif
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    @endif

    <!-- All Products Section -->
    <div class="py-12 bg-gray-50" id="products">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-10 animate-on-scroll">
                <h2 class="text-2xl md:text-3xl font-bold text-gray-900">All Products</h2>
                <p class="mt-3 text-lg text-gray-600 max-w-3xl mx-auto">
                    Browse our complete collection of digital products
                </p>
            </div>

            <div class="mb-8">
                <!-- Categories Filter -->
                <div class="flex flex-wrap gap-2 justify-center mb-8 animate-on-scroll">
                    <a href="{{ route('store.show', $seller->store_slug) }}"
                        class="px-4 py-2 rounded-full text-sm font-medium {{ !request()->route('category') ? 'bg-indigo-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-100' }} transition-colors duration-300 shadow-sm">
                        All
                    </a>
                    @if(isset($hasNewCategories) && $hasNewCategories)
                        @foreach ($categories as $catSlug => $catName)
                            <a href="{{ route('store.category', [$seller->store_slug, $catSlug]) }}"
                                class="px-4 py-2 rounded-full text-sm font-medium {{ request()->route('category') == $catSlug ? 'bg-indigo-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-100' }} transition-colors duration-300 shadow-sm">
                                {{ $catName }}
                            </a>
                        @endforeach
                    @else
                        @foreach ($categories as $category)
                            <a href="{{ route('store.category', [$seller->store_slug, $category]) }}"
                                class="px-4 py-2 rounded-full text-sm font-medium {{ request()->route('category') == $category ? 'bg-indigo-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-100' }} transition-colors duration-300 shadow-sm">
                                {{ $category }}
                            </a>
                        @endforeach
                    @endif
                </div>

                <!-- Products Grid -->
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 md:gap-8">
                    @foreach ($products as $product)
                        <div class="group relative card-hover bg-white rounded-xl overflow-hidden shadow-md border border-gray-100 animate-on-scroll" style="animation-delay: {{ $loop->index * 100 }}ms">
                            <div class="aspect-w-4 aspect-h-3 overflow-hidden bg-gray-100">
                                <a href="{{ route('store.product', [$seller->store_slug, $product->slug]) }}">
                                    <img src="{{ $product->image ? asset('storage/' . $product->image) : asset('images/placeholder.jpg') }}"
                                        alt="{{ $product->name }}"
                                        class="w-full h-full object-center object-cover transition-transform duration-500 group-hover:scale-110">
                                </a>
                                @if ($product->discount_price)
                                    <div
                                        class="absolute top-3 right-3 bg-red-500 text-white text-xs font-bold px-3 py-1.5 rounded-full shadow-sm">
                                        SALE {{ round((($product->price - $product->discount_price) / $product->price) * 100) }}% OFF
                                    </div>
                                @endif
                            </div>
                            <div class="p-5">
                                <h3 class="text-lg font-semibold text-gray-900 line-clamp-1">
                                    <a href="{{ route('store.product', [$seller->store_slug, $product->slug]) }}" class="hover:text-indigo-600 transition-colors duration-300">
                                        {{ $product->name }}
                                    </a>
                                </h3>
                                <p class="mt-1 text-sm text-gray-500">
                                    @if(isset($hasNewCategories) && $hasNewCategories && $product->productDetailedCategory)
                                        {{ $product->productDetailedCategory->name }}
                                    @elseif(isset($hasNewCategories) && $hasNewCategories && $product->productSubcategory)
                                        {{ $product->productSubcategory->name }}
                                    @elseif(isset($hasNewCategories) && $hasNewCategories && $product->productCategory)
                                        {{ $product->productCategory->name }}
                                    @else
                                        {{ ucfirst($product->category) }}
                                    @endif
                                </p>
                                <div class="mt-3 flex justify-between items-center">
                                    <div class="flex items-center">
                                        @for ($i = 1; $i <= 5; $i++)
                                            @if ($i <= ($product->average_rating ?? 5))
                                                <svg class="h-4 w-4 text-yellow-400" xmlns="http://www.w3.org/2000/svg"
                                                    viewBox="0 0 20 20" fill="currentColor">
                                                    <path
                                                        d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                                </svg>
                                            @else
                                                <svg class="h-4 w-4 text-gray-300" xmlns="http://www.w3.org/2000/svg"
                                                    viewBox="0 0 20 20" fill="currentColor">
                                                    <path
                                                        d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                                </svg>
                                            @endif
                                        @endfor
                                        <span class="text-xs text-gray-500 ml-1">({{ $product->reviews_count ?? 0 }})</span>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    @if ($product->discount_price)
                                        <p class="text-lg font-bold text-indigo-600">
                                            Rp {{ number_format($product->discount_price, 0, ',', '.') }}</p>
                                        <p class="text-sm text-gray-500 line-through">
                                            Rp {{ number_format($product->price, 0, ',', '.') }}</p>
                                    @else
                                        <p class="text-lg font-bold text-indigo-600">
                                            Rp {{ number_format($product->price, 0, ',', '.') }}</p>
                                    @endif
                                </div>
                                <div class="mt-4">
                                    <a href="{{ route('store.product', [$seller->store_slug, $product->slug]) }}"
                                       class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 transition-colors duration-300">
                                        View Details
                                    </a>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="mt-12">
                    <div class="pagination-container">
                        {{ $products->links('vendor.pagination.custom-tailwind') }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Testimonials -->
    @if(isset($testimonials) && count($testimonials) > 0)
    <div class="py-16 bg-gray-50">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12 animate-on-scroll">
                <h2 class="text-3xl font-bold text-gray-900 sm:text-4xl">What Our Customers Say</h2>
                <p class="mt-3 text-xl text-gray-600 max-w-3xl mx-auto">
                    Read testimonials from our satisfied customers who have used our digital products
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-{{ count($testimonials) >= 3 ? '3' : (count($testimonials) == 2 ? '2' : '1') }} gap-8 max-w-5xl mx-auto">
                @foreach ($testimonials as $testimonial)
                    <div class="bg-white p-6 rounded-xl border border-gray-100 shadow-sm card-hover animate-on-scroll" style="animation-delay: {{ $loop->index * 100 }}ms">
                        <div class="flex items-center mb-3">
                            <div class="flex">
                                @for($i = 1; $i <= 5; $i++)
                                    @if($i <= $testimonial['rating'])
                                        <svg class="h-4 w-4 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                        </svg>
                                    @else
                                        <svg class="h-4 w-4 text-gray-300" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                        </svg>
                                    @endif
                                @endfor
                            </div>
                        </div>
                        <p class="text-gray-600 italic mb-5 text-lg leading-relaxed">"{{ $testimonial['quote'] }}"</p>
                        <div class="flex items-center">
                            <div class="bg-purple-100 rounded-full w-12 h-12 flex items-center justify-center text-purple-700 font-bold text-xl mr-3">
                                {{ substr($testimonial['author'], 0, 1) }}
                            </div>
                            <div>
                                <p class="text-gray-900 font-semibold">{{ $testimonial['author'] }}</p>
                                <p class="text-gray-500 text-sm">{{ $testimonial['role'] }}</p>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
    @endif

    <!-- Newsletter -->
    {{-- <div class="py-16 hero-gradient relative overflow-hidden">
        <div class="absolute inset-0 bg-indigo-600 opacity-90"></div>
        <div class="absolute right-0 top-0 w-1/3 h-full opacity-10">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white" class="w-full h-full">
                <path d="M1.5 8.67v8.58a3 3 0 003 3h15a3 3 0 003-3V8.67l-8.928 5.493a3 3 0 01-3.144 0L1.5 8.67z" />
                <path d="M22.5 6.908V6.75a3 3 0 00-3-3h-15a3 3 0 00-3 3v.158l9.714 5.978a1.5 1.5 0 001.572 0L22.5 6.908z" />
            </svg>
        </div>
        <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="max-w-3xl mx-auto text-center animate-on-scroll">
                <h2 class="text-3xl font-bold text-white mb-4 sm:text-4xl">Subscribe to Our Newsletter</h2>
                <p class="text-xl text-indigo-100 mb-8 leading-relaxed">Get updates on new products, special offers, and helpful tips to make the most of our digital products</p>
                <form class="max-w-lg mx-auto">
                    <div class="flex flex-col sm:flex-row gap-4">
                        <input type="email" placeholder="Enter your email address"
                            class="flex-1 rounded-lg border-0 py-3.5 px-5 text-gray-900 shadow-lg focus:ring-2 focus:ring-inset focus:ring-indigo-300 text-base">
                        <button type="submit"
                            class="rounded-lg bg-white px-6 py-3.5 text-base font-semibold text-indigo-700 shadow-lg hover:bg-indigo-50 transition-colors duration-300">
                            Subscribe Now
                        </button>
                    </div>
                    <p class="mt-4 text-sm text-indigo-200">We respect your privacy. Unsubscribe at any time.</p>
                </form>
            </div>
        </div>
    </div> --}}
@endsection





