@extends('layouts.user-dashboard')

@section('content')
    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
        <div class="px-4 py-5 sm:px-6">
            <h1 class="text-2xl font-bold text-gray-900">My Purchases</h1>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">View all your purchased products</p>
        </div>

        <div class="border-t border-gray-200">
            <div class="bg-white px-4 py-5 sm:p-6">
                @if (count($purchases ?? []) > 0)
                    <ul role="list" class="divide-y divide-gray-200">
                        @foreach ($purchases as $purchase)
                            <li class="py-4 flex">
                                <div class="flex-shrink-0 h-16 w-16 border border-gray-200 rounded-md overflow-hidden">
                                    <img src="{{ $purchase->product->image ? asset('storage/' . $purchase->product->image) : asset('images/placeholder.jpg') }}"
                                        alt="{{ $purchase->product->name }}"
                                        class="h-full w-full object-center object-cover">
                                </div>
                                <div class="ml-4 flex-1 flex flex-col">
                                    <div>
                                        <div class="flex justify-between text-base font-medium text-gray-900">
                                            <h3>{{ $purchase->product->name }}</h3>
                                            <p class="ml-4">Rp {{ number_format($purchase->amount, 0, ',', '.') }}</p>
                                        </div>
                                        <p class="mt-1 text-sm text-gray-500">
                                            @if($purchase->product->productDetailedCategory)
                                                {{ $purchase->product->productDetailedCategory->name }}
                                            @elseif($purchase->product->productSubcategory)
                                                {{ $purchase->product->productSubcategory->name }}
                                            @elseif($purchase->product->productCategory)
                                                {{ $purchase->product->productCategory->name }}
                                            @else
                                                {{ $purchase->product->category }}
                                            @endif
                                        </p>
                                    </div>
                                    <div class="flex-1 flex items-end justify-between text-sm">
                                        <p class="text-gray-500">Purchased on {{ $purchase->created_at->format('M d, Y') }}
                                        </p>
                                        {{-- <div class="flex">
                                        <a href="#" class="font-medium text-indigo-600 hover:text-indigo-500">Download</a>
                                    </div> --}}
                                        <form action="{{ route('product.download', $purchase->product->id) }}"
                                            method="POST">
                                            @csrf
                                            <button type="submit"
                                                class="font-medium text-indigo-600 hover:text-indigo-500">
                                                Download
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </li>
                        @endforeach
                    </ul>

                    <div class="mt-6 text-center">
                        <a href="{{ route('user.browse') }}"
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Browse More Products
                        </a>
                    </div>
                @else
                    <div class="text-center py-12">
                        <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No purchases yet</h3>
                        <p class="mt-1 text-sm text-gray-500">Get started by exploring our products.</p>
                        <div class="mt-6">
                            <a href="{{ route('user.browse') }}"
                                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                Browse Products
                            </a>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Call to Action -->
    <div class="mt-16 bg-gradient-to-r from-purple-600 to-indigo-600 rounded-xl p-8 text-center text-white">
        <h2 class="text-2xl font-bold mb-4">Ready to Start Selling Your Digital Products?</h2>
        <p class="mb-6 max-w-2xl mx-auto">Join thousands of creators who are earning by selling their digital products
            on Digitora.</p>
        <a href="{{ route('seller.apply') }}"
            class="inline-block bg-white text-purple-600 px-6 py-3 rounded-full font-medium hover:bg-gray-100 transition-colors duration-300">
            Become a Seller Today
        </a>
    </div>

@endsection
