@extends('store.layout')

@section('content')
    <div class="py-8 bg-white">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Breadcrumbs -->
            <nav class="flex mb-8" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <a href="{{ route('store.show', $seller->store_slug) }}"
                            class="text-gray-500 hover:text-indigo-600 text-sm">
                            Home
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20"
                                xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd"
                                    d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                                    clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-gray-500 ml-1 text-sm">About</span>
                        </div>
                    </li>
                </ol>
            </nav>

            <!-- About Section -->
            <div class="bg-gray-50 rounded-lg p-8">
                <h1 class="text-3xl font-bold text-gray-900 mb-4">About {{ $seller->store_name }}</h1>
                {{-- <p class="text-gray-600 mb-6">
                    {{ $seller->sellerApplication->store_description ?? $seller->store_description ?? 'We are passionate about creating high-quality digital products to help you succeed.' }}
                </p> --}}

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h2 class="text-xl font-semibold text-gray-900 mb-2">Our Story</h2>
                        <p class="text-gray-500">
                            @if($seller->sellerApplication && !empty($seller->sellerApplication->store_description))
                                {{ $seller->sellerApplication->store_description }}
                            @else
                                {{ $seller->store_name }} was founded with a mission to provide top-notch digital solutions for
                                professionals, entrepreneurs, and hobbyists alike. We specialize in creating products that are
                                easy to use, highly functional, and designed to save you time and effort.
                            @endif
                        </p>
                    </div>
                    <div>
                        <h2 class="text-xl font-semibold text-gray-900 mb-2">Our Products</h2>
                        <p class="text-gray-500">
                            We offer a wide range of digital products
                            @if(count($categories) > 0)
                                including {{ implode(', ', array_slice($categories, 0, -1)) }}{{ count($categories) > 1 ? ' and ' . end($categories) : (count($categories) == 1 ? ' ' . end($categories) : '') }}
                            @else
                                for various needs
                            @endif.
                            With over
                            {{ $productCount }}
                            active {{ Str::plural('product', $productCount) }}, we're committed to delivering value to our customers.
                        </p>
                    </div>
                </div>

                <div class="mt-8">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">Contact Us</h2>
                    <p class="text-gray-500 mb-2">
                        Have questions or need support? Reach out to us!
                    </p>
                    <p class="text-gray-500">
                        <strong>Email:</strong> {{ $seller->email }}<br>
                        <strong>Support Hours:</strong> Monday - Friday, 9 AM - 5 PM
                    </p>
                    <a href="mailto:{{ $seller->email }}"
                        class="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700">
                        Get in Touch
                    </a>
                </div>
            </div>
        </div>
    </div>
@endsection
