<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Lara<PERSON>\Sanctum\HasApiTokens;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'is_seller',
        'is_admin',
        'is_superadmin',
        'phone',
        'bio',
        'avatar',
        'notification_order',
        'notification_payment',
        'notification_product',
        'notification_marketing',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'is_seller' => 'boolean',
        'is_admin' => 'boolean',
        'is_superadmin' => 'boolean',
        'email_verified_at' => 'datetime',
        'notification_order' => 'boolean',
        'notification_payment' => 'boolean',
        'notification_product' => 'boolean',
        'notification_marketing' => 'boolean',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($user) {
            // If no password is set, generate a random secure password
            if (empty($user->password)) {
                $user->password = \Illuminate\Support\Facades\Hash::make(\Illuminate\Support\Str::random(16));
            }
        });
    }

    /**
     * Check if the user is a seller
     *
     * @return bool
     */
    public function isSeller(): bool
    {
        return $this->is_seller;
    }

    /**
     * Check if the user is an admin
     *
     * @return bool
     */
    public function isAdmin(): bool
    {
        return $this->is_admin;
    }

    /**
     * Check if the user is a superadmin
     *
     * @return bool
     */
    public function isSuperAdmin(): bool
    {
        return $this->is_superadmin;
    }

    /**
     * Check if the user is a regular user (not seller, admin, or superadmin)
     *
     * @return bool
     */
    public function isRegularUser(): bool
    {
        return !$this->is_seller && !$this->is_admin && !$this->is_superadmin;
    }

    public function sellerApplication()
    {
        return $this->hasOne(SellerApplication::class);
    }

    // Accessor for store_name
    public function getStoreNameAttribute()
    {
        return $this->sellerApplication ? $this->sellerApplication->store_name : null;
    }

    // Accessor for store_description
    public function getStoreDescriptionAttribute()
    {
        return $this->sellerApplication ? $this->sellerApplication->store_description : null;
    }

    // Accessor for store_category
    public function getStoreCategoryAttribute()
    {
        return $this->sellerApplication ? $this->sellerApplication->store_category : null;
    }

    // Accessor for store_logo
    public function getStoreLogoAttribute()
    {
        return $this->sellerApplication ? $this->sellerApplication->store_logo : null;
    }

    // social account
    public function socialAccounts()
    {
        return $this->hasMany(SocialAccount::class);
    }

    //products
    public function products()
    {
        return $this->hasMany(Product::class, 'seller_id', 'id');
    }
}
