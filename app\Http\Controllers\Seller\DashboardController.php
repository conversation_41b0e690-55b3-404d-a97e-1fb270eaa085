<?php

namespace App\Http\Controllers\Seller;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\Product;
use App\Models\Review;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    public function index(Request $request)
    {
        $user = Auth::user();
        $timeRange = $request->get('range', '7d'); // Default to 7 days

        // Set date range based on selected time range
        $startDate = null;
        $endDate = Carbon::now();

        switch ($timeRange) {
            case '7d':
                $startDate = Carbon::now()->subDays(7);
                break;
            case '30d':
                $startDate = Carbon::now()->subDays(30);
                break;
            case '90d':
                $startDate = Carbon::now()->subDays(90);
                break;
            default:
                $startDate = Carbon::now()->subDays(7);
        }

        // Get dashboard stats with explicit fallbacks - only for successful orders
        $totalRevenue = Order::whereHas('product', function ($query) use ($user) {
                $query->where('seller_id', $user->id);
            })
            ->where('status', 'success')
            ->where('payment_status', 'paid')
            ->sum('amount') ?? 0;

        $totalSales = Order::whereHas('product', function ($query) use ($user) {
                $query->where('seller_id', $user->id);
            })
            ->where('status', 'success')
            ->count() ?? 0;

        $activeProducts = Product::where('seller_id', $user->id)
            ->where('status', 'active')
            ->count() ?? 0;

        // Calculate average rating dynamically
        $avgRating = Review::whereHas('product', function ($query) use ($user) {
            $query->where('seller_id', $user->id);
        })->average('rating') ?? 0;
        $avgRating = number_format($avgRating, 1); // Format to one decimal place

        // Get recent successful sales only
        $recentSales = Order::whereHas('product', function ($query) use ($user) {
                $query->where('seller_id', $user->id);
            })
            ->where('status', 'success')
            ->with(['product', 'user'])
            ->orderBy('created_at', 'desc')
            ->take(4)
            ->get();

        // Get top selling products (only count successful orders)
        $topSellingProducts = Product::where('seller_id', $user->id)
            ->withCount(['orders' => function ($query) {
                $query->where('status', 'success');
            }])
            ->orderBy('orders_count', 'desc')
            ->take(3)
            ->get();

        // Get revenue data for chart - only for successful orders
        $revenueData = DB::table('orders')
            ->join('products', 'orders.product_id', '=', 'products.id')
            ->select(
                DB::raw('DATE(orders.created_at) as date'),
                DB::raw('SUM(orders.amount) as revenue')
            )
            ->where('products.seller_id', '=', $user->id)
            ->where('orders.status', '=', 'success')
            ->where('orders.payment_status', '=', 'paid')
            ->whereBetween('orders.created_at', [$startDate, $endDate])
            ->groupBy(DB::raw('DATE(orders.created_at)'))
            ->orderBy('date', 'asc') // Ensure chronological order for cumulative calculation
            ->get();

        // Ensure we have at least one data point for charts
        if ($revenueData->isEmpty()) {
            $revenueData = collect([
                (object)['date' => now()->format('Y-m-d'), 'revenue' => 0]
            ]);
        }

        // Ensure all variables are passed to the view
        return view('seller.dashboard', [
            'totalRevenue' => $totalRevenue,
            'totalSales' => $totalSales,
            'activeProducts' => $activeProducts,
            'avgRating' => $avgRating,
            'recentSales' => $recentSales,
            'topSellingProducts' => $topSellingProducts,
            'revenueData' => $revenueData,
            'timeRange' => $timeRange,
        ]);
    }

    public function analytics()
    {
        return view('seller.analytics');
    }

    public function payments()
    {
        return view('seller.payments');
    }

    public function settings()
    {
        return view('seller.settings');
    }
}