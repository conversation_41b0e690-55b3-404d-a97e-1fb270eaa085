<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">

<head>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-QWR2LGRD93"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-QWR2LGRD93');
    </script>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <title><?php echo e(config('app.name', 'Digitora')); ?> - Seller Dashboard</title>

    <link rel="icon" type="image/x-icon" href="<?php echo e(asset('images/digitora-logo.png')); ?>">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Scripts -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
    <link href="<?php echo e(asset('css/product.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(asset('css/dashboard-seller.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(asset('css/analytics.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(asset('css/ai-chat.css')); ?>" rel="stylesheet">

    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
</head>

<body class="font-sans antialiased">
    <div class="flex min-h-screen bg-gray-50">
        <!-- Sidebar for desktop -->
        <aside class="hidden w-64 flex-col border-r bg-white lg:flex">
            <div class="flex h-16 items-center border-b px-4">
                <a href="<?php echo e(route('home')); ?>" class="flex items-center gap-2">
                    <div
                        class="flex h-8 w-8 items-center justify-center rounded-md bg-gradient-to-br from-indigo-600 to-purple-700 text-white">
                        <span class="text-lg font-bold">D</span>
                    </div>
                    <span class="text-xl font-bold">Digitora</span>
                </a>
            </div>
            <nav class="flex-1 overflow-auto py-4">
                <div class="px-3">
                    <h2 class="mb-2 px-4 text-xs font-semibold uppercase tracking-wider text-gray-500">Main</h2>
                    <div class="space-y-1">
                        <a href="<?php echo e(route('seller.dashboard')); ?>"
                            class="flex items-center gap-3 rounded-md px-4 py-2 text-sm font-medium transition-colors <?php echo e(request()->routeIs('seller.dashboard') ? 'bg-indigo-50 text-indigo-700' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round"
                                class="<?php echo e(request()->routeIs('seller.dashboard') ? 'text-indigo-700' : 'text-gray-500'); ?>">
                                <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                                <polyline points="9 22 9 12 15 12 15 22"></polyline>
                            </svg>
                            Dashboard
                        </a>
                        <a href="<?php echo e(route('seller.products.index')); ?>"
                            class="flex items-center gap-3 rounded-md px-4 py-2 text-sm font-medium transition-colors <?php echo e(request()->routeIs('seller.products.*') ? 'bg-indigo-50 text-indigo-700' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round"
                                class="<?php echo e(request()->routeIs('seller.products.*') ? 'text-indigo-700' : 'text-gray-500'); ?>">
                                <path d="M21 8a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8Z">
                                </path>
                                <path d="M21 6V4a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v2"></path>
                            </svg>
                            Products
                        </a>
                        <a href="<?php echo e(route('seller.orders.index')); ?>"
                            class="flex items-center gap-3 rounded-md px-4 py-2 text-sm font-medium transition-colors <?php echo e(request()->routeIs('seller.orders.*') ? 'bg-indigo-50 text-indigo-700' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round"
                                class="<?php echo e(request()->routeIs('seller.orders.*') ? 'text-indigo-700' : 'text-gray-500'); ?>">
                                <circle cx="8" cy="21" r="1"></circle>
                                <circle cx="19" cy="21" r="1"></circle>
                                <path
                                    d="M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12">
                                </path>
                            </svg>
                            Orders
                        </a>
                        <a href="<?php echo e(route('seller.analytics')); ?>"
                            class="flex items-center gap-3 rounded-md px-4 py-2 text-sm font-medium transition-colors <?php echo e(request()->routeIs('seller.analytics') ? 'bg-indigo-50 text-indigo-700' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round"
                                class="<?php echo e(request()->routeIs('seller.analytics') ? 'text-indigo-700' : 'text-gray-500'); ?>">
                                <path d="M3 3v18h18"></path>
                                <path d="m19 9-5 5-4-4-3 3"></path>
                            </svg>
                            Analytics
                        </a>
                        <a href="<?php echo e(route('seller.payments.index')); ?>"
                            class="flex items-center gap-3 rounded-md px-4 py-2 text-sm font-medium transition-colors <?php echo e(request()->routeIs('seller.payments.*') ? 'bg-indigo-50 text-indigo-700' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round"
                                class="<?php echo e(request()->routeIs('seller.payments.*') ? 'text-indigo-700' : 'text-gray-500'); ?>">
                                <rect width="20" height="14" x="2" y="5" rx="2"></rect>
                                <line x1="2" x2="22" y1="10" y2="10"></line>
                            </svg>
                            Payments
                        </a>
                        <a href="<?php echo e(route('seller.settings')); ?>"
                            class="flex items-center gap-3 rounded-md px-4 py-2 text-sm font-medium transition-colors <?php echo e(request()->routeIs('seller.settings') ? 'bg-indigo-50 text-indigo-700' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                stroke-linecap="round" stroke-linejoin="round"
                                class="<?php echo e(request()->routeIs('seller.settings') ? 'text-indigo-700' : 'text-gray-500'); ?>">
                                <path
                                    d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z">
                                </path>
                                <circle cx="12" cy="12" r="3"></circle>
                            </svg>
                            Settings
                        </a>
                    </div>
                </div>
                <div class="mt-6 px-3">
                    <h2 class="mb-2 px-4 text-xs font-semibold uppercase tracking-wider text-gray-500">My Store</h2>
                    <div class="space-y-1">
                        <?php if(Auth::user()->sellerApplication): ?>
                            <a href="<?php echo e(route('store.show', Auth::user()->sellerApplication->store_name_slug)); ?>"
                                class="flex items-center gap-3 rounded-md px-4 py-2 text-sm font-medium transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900">
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round" class="text-gray-500">
                                    <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                                    <polyline points="9 22 9 12 15 12 15 22"></polyline>
                                </svg>
                                View Store
                            </a>
                        <?php endif; ?>
                        <a href="<?php echo e(route('user.dashboard')); ?>"
                            class="flex items-center gap-3 rounded-md px-4 py-2 text-sm font-medium transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                stroke-linecap="round" stroke-linejoin="round" class="text-gray-500">
                                <rect x="3" y="3" width="18" height="18" rx="2" ry="2">
                                </rect>
                                <line x1="3" y1="9" x2="21" y2="9"></line>
                                <line x1="9" y1="21" x2="9" y2="9"></line>
                            </svg>
                            User Dashboard
                        </a>
                    </div>
                </div>

                <div class="mt-6 px-3">
                    <h2 class="mb-2 px-4 text-xs font-semibold uppercase tracking-wider text-gray-500">Support</h2>
                    <div class="space-y-1">
                        <a href="<?php echo e(route('seller.help-center.index')); ?>"
                            class="flex items-center gap-3 rounded-md px-4 py-2 text-sm font-medium transition-colors <?php echo e(request()->routeIs('seller.help-center.*') ? 'bg-indigo-50 text-indigo-700' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                stroke-linecap="round" stroke-linejoin="round"
                                class="<?php echo e(request()->routeIs('seller.help-center.*') ? 'text-indigo-700' : 'text-gray-500'); ?>">
                                <circle cx="12" cy="12" r="10"></circle>
                                <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                                <path d="M12 17h.01"></path>
                            </svg>
                            Help Center
                        </a>
                        <a href="<?php echo e(route('seller.documentation.index')); ?>"
                            class="flex items-center gap-3 rounded-md px-4 py-2 text-sm font-medium transition-colors <?php echo e(request()->routeIs('seller.documentation.*') ? 'bg-indigo-50 text-indigo-700' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                stroke-linecap="round" stroke-linejoin="round"
                                class="<?php echo e(request()->routeIs('seller.documentation.*') ? 'text-indigo-700' : 'text-gray-500'); ?>">
                                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                <polyline points="14 2 14 8 20 8"></polyline>
                                <line x1="16" y1="13" x2="8" y2="13"></line>
                                <line x1="16" y1="17" x2="8" y2="17"></line>
                                <line x1="10" y1="9" x2="8" y2="9"></line>
                            </svg>
                            Documentation
                        </a>
                    </div>
                </div>
            </nav>
            <div class="border-t p-4">
                <div class="flex items-center gap-3">
                    <div class="h-10 w-10 rounded-full bg-gradient-to-br from-indigo-400 to-purple-400"></div>
                    <div>
                        <p class="text-sm font-medium"><?php echo e(Auth::user()->name); ?></p>
                        <p class="text-xs text-gray-500">Seller Account</p>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Main content -->
        <div class="flex flex-1 flex-col">
            <!-- Header -->
            <header class="flex h-16 items-center justify-between border-b bg-white px-4 sm:px-6">
                <div class="flex items-center">
                    <button x-data @click="$dispatch('toggle-sidebar')"
                        class="mr-2 rounded-md p-2 text-gray-500 hover:bg-gray-100 hover:text-gray-600 lg:hidden">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round" class="h-6 w-6">
                            <line x1="4" x2="20" y1="12" y2="12"></line>
                            <line x1="4" x2="20" y1="6" y2="6"></line>
                            <line x1="4" x2="20" y1="18" y2="18"></line>
                        </svg>
                        <span class="sr-only">Toggle Menu</span>
                    </button>
                    <a href="<?php echo e(route('home')); ?>" class="flex items-center gap-2 lg:hidden">
                        <div
                            class="flex h-8 w-8 items-center justify-center rounded-md bg-gradient-to-br from-indigo-600 to-purple-700 text-white">
                            <span class="text-lg font-bold">D</span>
                        </div>
                    </a>
                </div>
                <div class="relative flex-1 max-w-xs mx-4 hidden sm:block">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                        stroke-linejoin="round" class="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500">
                        <circle cx="11" cy="11" r="8"></circle>
                        <path d="m21 21-4.3-4.3"></path>
                    </svg>
                    <input type="search" placeholder="Search products, orders..."
                        class="w-full rounded-md border border-gray-300 bg-gray-50 py-2 pl-8 pr-4 text-sm focus:border-indigo-500 focus:bg-white focus:outline-none focus:ring-1 focus:ring-indigo-500">
                </div>
                <div class="flex items-center gap-3">
                    <button class="relative rounded-md p-2 text-gray-500 hover:bg-gray-100 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round" class="h-5 w-5">
                            <path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9"></path>
                            <path d="M10.3 21a1.94 1.94 0 0 0 3.4 0"></path>
                        </svg>
                        <span class="absolute right-1 top-1 h-2 w-2 rounded-full bg-red-500"></span>
                        <span class="sr-only">Notifications</span>
                    </button>
                    <div x-data="{ open: false }" class="relative">
                        <button @click="open = !open" class="relative h-8 w-8 rounded-full">
                            <div class="h-8 w-8 rounded-full bg-gradient-to-br from-indigo-400 to-purple-400"></div>
                        </button>
                        <div x-show="open" @click.away="open = false"
                            class="absolute right-0 mt-2 w-48 rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 z-10">
                            <div class="px-4 py-2 text-xs text-gray-500">
                                My Account
                            </div>
                            <a href="<?php echo e(route('profile.edit')); ?>"
                                class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round" class="mr-2 h-4 w-4">
                                    <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
                                    <circle cx="12" cy="7" r="4"></circle>
                                </svg>
                                <span>Profile</span>
                            </a>
                            <a href="<?php echo e(route('seller.settings')); ?>"
                                class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round" class="mr-2 h-4 w-4">
                                    <path
                                        d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z">
                                    </path>
                                    <circle cx="12" cy="12" r="3"></circle>
                                </svg>
                                <span>Settings</span>
                            </a>
                            <div class="border-t border-gray-100"></div>
                            <form method="POST" action="<?php echo e(route('logout')); ?>">
                                <?php echo csrf_field(); ?>
                                <button type="submit"
                                    class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                        stroke-linecap="round" stroke-linejoin="round" class="mr-2 h-4 w-4">
                                        <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                                        <polyline points="16 17 21 12 16 7"></polyline>
                                        <line x1="21" y1="12" x2="9" y2="12"></line>
                                    </svg>
                                    <span>Log out</span>
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Mobile sidebar -->
            <div x-data="{ open: false }" @toggle-sidebar.window="open = !open" x-show="open"
                class="fixed inset-0 z-50 lg:hidden" x-cloak>
                <div x-show="open" @click="open = false" class="fixed inset-0 bg-gray-600 bg-opacity-75"></div>
                <div class="fixed inset-y-0 left-0 flex w-64 flex-col bg-white">
                    <div class="flex h-16 items-center justify-between border-b px-4">
                        <a href="<?php echo e(route('home')); ?>" class="flex items-center gap-2">
                            <div
                                class="flex h-8 w-8 items-center justify-center rounded-md bg-gradient-to-br from-indigo-600 to-purple-700 text-white">
                                <span class="text-lg font-bold">D</span>
                            </div>
                            <span class="text-xl font-bold">Digitora</span>
                        </a>
                        <button @click="open = false"
                            class="rounded-md p-2 text-gray-500 hover:bg-gray-100 hover:text-gray-600">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6">
                                <line x1="18" y1="6" x2="6" y2="18"></line>
                                <line x1="6" y1="6" x2="18" y2="18"></line>
                            </svg>
                            <span class="sr-only">Close Menu</span>
                        </button>
                    </div>
                    <nav class="flex-1 overflow-auto py-4">
                        <div class="px-3">
                            <h2 class="mb-2 px-4 text-xs font-semibold uppercase tracking-wider text-gray-500">Main
                            </h2>
                            <div class="space-y-1">
                                <a href="<?php echo e(route('seller.dashboard')); ?>"
                                    class="flex items-center gap-3 rounded-md px-4 py-2 text-sm font-medium transition-colors <?php echo e(request()->routeIs('seller.dashboard') ? 'bg-indigo-50 text-indigo-700' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'); ?>">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                        stroke-linecap="round" stroke-linejoin="round"
                                        class="<?php echo e(request()->routeIs('seller.dashboard') ? 'text-indigo-700' : 'text-gray-500'); ?>">
                                        <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                                        <polyline points="9 22 9 12 15 12 15 22"></polyline>
                                    </svg>
                                    Dashboard
                                </a>
                                <a href="<?php echo e(route('seller.products.index')); ?>"
                                    class="flex items-center gap-3 rounded-md px-4 py-2 text-sm font-medium transition-colors <?php echo e(request()->routeIs('seller.products.*') ? 'bg-indigo-50 text-indigo-700' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'); ?>">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                        stroke-linecap="round" stroke-linejoin="round"
                                        class="<?php echo e(request()->routeIs('seller.products.*') ? 'text-indigo-700' : 'text-gray-500'); ?>">
                                        <path
                                            d="M21 8a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8Z">
                                        </path>
                                        <path d="M21 6V4a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v2"></path>
                                    </svg>
                                    Products
                                </a>
                                <a href="<?php echo e(route('seller.orders.index')); ?>"
                                    class="flex items-center gap-3 rounded-md px-4 py-2 text-sm font-medium transition-colors <?php echo e(request()->routeIs('seller.orders.*') ? 'bg-indigo-50 text-indigo-700' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'); ?>">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                        stroke-linecap="round" stroke-linejoin="round"
                                        class="<?php echo e(request()->routeIs('seller.orders.*') ? 'text-indigo-700' : 'text-gray-500'); ?>">
                                        <circle cx="8" cy="21" r="1"></circle>
                                        <circle cx="19" cy="21" r="1"></circle>
                                        <path
                                            d="M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12">
                                        </path>
                                    </svg>
                                    Orders
                                </a>
                                <a href="<?php echo e(route('seller.analytics')); ?>"
                                    class="flex items-center gap-3 rounded-md px-4 py-2 text-sm font-medium transition-colors <?php echo e(request()->routeIs('seller.analytics') ? 'bg-indigo-50 text-indigo-700' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'); ?>">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                        stroke-linecap="round" stroke-linejoin="round"
                                        class="<?php echo e(request()->routeIs('seller.analytics') ? 'text-indigo-700' : 'text-gray-500'); ?>">
                                        <path d="M3 3v18h18"></path>
                                        <path d="m19 9-5 5-4-4-3 3"></path>
                                    </svg>
                                    Analytics
                                </a>
                                <a href="<?php echo e(route('seller.payments.index')); ?>"
                                    class="flex items-center gap-3 rounded-md px-4 py-2 text-sm font-medium transition-colors <?php echo e(request()->routeIs('seller.payments.*') ? 'bg-indigo-50 text-indigo-700' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'); ?>">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                        stroke-linecap="round" stroke-linejoin="round"
                                        class="<?php echo e(request()->routeIs('seller.payments.*') ? 'text-indigo-700' : 'text-gray-500'); ?>">
                                        <rect width="20" height="14" x="2" y="5" rx="2"></rect>
                                        <line x1="2" x2="22" y1="10" y2="10"></line>
                                    </svg>
                                    Payments
                                </a>
                                <a href="<?php echo e(route('seller.settings')); ?>"
                                    class="flex items-center gap-3 rounded-md px-4 py-2 text-sm font-medium transition-colors <?php echo e(request()->routeIs('seller.settings') ? 'bg-indigo-50 text-indigo-700' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'); ?>">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                        stroke-linecap="round" stroke-linejoin="round"
                                        class="<?php echo e(request()->routeIs('seller.settings') ? 'text-indigo-700' : 'text-gray-500'); ?>">
                                        <path
                                            d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z">
                                        </path>
                                        <circle cx="12" cy="12" r="3"></circle>
                                    </svg>
                                    Settings
                                </a>
                            </div>
                        </div>
                        <div class="mt-6 px-3">
                            <h2 class="mb-2 px-4 text-xs font-semibold uppercase tracking-wider text-gray-500">My Store
                            </h2>
                            <div class="space-y-1">
                                <?php if(Auth::user()->sellerApplication): ?>
                                    <a href="<?php echo e(route('store.show', Auth::user()->sellerApplication->store_name_slug)); ?>"
                                        class="flex items-center gap-3 rounded-md px-4 py-2 text-sm font-medium transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                            stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                            class="text-gray-500">
                                            <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                                            <polyline points="9 22 9 12 15 12 15 22"></polyline>
                                        </svg>
                                        View Store
                                    </a>
                                <?php endif; ?>
                                <a href="<?php echo e(route('user.dashboard')); ?>"
                                    class="flex items-center gap-3 rounded-md px-4 py-2 text-sm font-medium transition-colors text-gray-700 hover:bg-gray-100 hover:text-gray-900">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                        stroke-linecap="round" stroke-linejoin="round" class="text-gray-500">
                                        <rect x="3" y="3" width="18" height="18" rx="2"
                                            ry="2"></rect>
                                        <line x1="3" y1="9" x2="21" y2="9"></line>
                                        <line x1="9" y1="21" x2="9" y2="9"></line>
                                    </svg>
                                    User Dashboard
                                </a>
                            </div>
                        </div>

                        <div class="mt-6 px-3">
                            <h2 class="mb-2 px-4 text-xs font-semibold uppercase tracking-wider text-gray-500">Support
                            </h2>
                            <div class="space-y-1">
                                <a href="<?php echo e(route('seller.help-center.index')); ?>"
                                    class="flex items-center gap-3 rounded-md px-4 py-2 text-sm font-medium transition-colors <?php echo e(request()->routeIs('seller.help-center.*') ? 'bg-indigo-50 text-indigo-700' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'); ?>">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                        stroke-linecap="round" stroke-linejoin="round"
                                        class="<?php echo e(request()->routeIs('seller.help-center.*') ? 'text-indigo-700' : 'text-gray-500'); ?>">
                                        <circle cx="12" cy="12" r="10"></circle>
                                        <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                                        <path d="M12 17h.01"></path>
                                    </svg>
                                    Help Center
                                </a>
                                <a href="<?php echo e(route('seller.documentation.index')); ?>"
                                    class="flex items-center gap-3 rounded-md px-4 py-2 text-sm font-medium transition-colors <?php echo e(request()->routeIs('seller.documentation.*') ? 'bg-indigo-50 text-indigo-700' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'); ?>">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                        stroke-linecap="round" stroke-linejoin="round"
                                        class="<?php echo e(request()->routeIs('seller.documentation.*') ? 'text-indigo-700' : 'text-gray-500'); ?>">
                                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                        <polyline points="14 2 14 8 20 8"></polyline>
                                        <line x1="16" y1="13" x2="8" y2="13"></line>
                                        <line x1="16" y1="17" x2="8" y2="17"></line>
                                        <line x1="10" y1="9" x2="8" y2="9"></line>
                                    </svg>
                                    Documentation
                                </a>
                            </div>
                        </div>
                    </nav>
                    <div class="border-t p-4">
                        <div class="flex items-center gap-3">
                            <div class="h-10 w-10 rounded-full bg-gradient-to-br from-indigo-400 to-purple-400"></div>
                            <div>
                                <p class="text-sm font-medium"><?php echo e(Auth::user()->name); ?></p>
                                <p class="text-xs text-gray-500">Seller Account</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Page content -->
            <main class="flex-1 overflow-auto p-4 sm:p-6">
                <?php if(session('success')): ?>
                    <div class="mb-4 rounded-md bg-green-50 p-4 text-sm text-green-700">
                        <?php echo e(session('success')); ?>

                    </div>
                <?php endif; ?>

                <?php if(session('error')): ?>
                    <div class="mb-4 rounded-md bg-red-50 p-4 text-sm text-red-700">
                        <?php echo e(session('error')); ?>

                    </div>
                <?php endif; ?>

                <?php echo $__env->yieldContent('content'); ?>
            </main>
        </div>
    </div>

    <script src="<?php echo e(asset(js_path() . '/product.js') . '?v=' . strtotime('now')); ?>" defer></script>

    <!-- Scripts pushed from child views -->
    <?php echo $__env->yieldPushContent('scripts'); ?>

    <!-- AI Chat Component -->
    <?php echo $__env->make('components.ai-chat', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <!-- AI Chat JS -->
    <script src="<?php echo e(asset(js_path() . '/ai-chat.js')); ?>" defer></script>
</body>

</html>
<?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\digitora\resources\views/seller/layouts/app.blade.php ENDPATH**/ ?>