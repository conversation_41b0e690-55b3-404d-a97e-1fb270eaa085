@extends('seller.layouts.app')

@section('content')
    <style>
        /* Ensure chart containers have proper dimensions */
        canvas {
            display: block;
            box-sizing: border-box;
            height: 100% !important;
            width: 100% !important;
        }
    </style>
    <div class="space-y-6">
        <div class="flex flex-col justify-between gap-4 md:flex-row md:items-center">
            <div>
                <h1 class="text-3xl font-bold tracking-tight text-gray-900">Analytics</h1>
                <p class="text-gray-600">Track your store performance and sales</p>
            </div>
            <div class="flex items-center gap-3">
                <div x-data="{ open: false }" class="relative">
                    <button @click="open = !open"
                        class="inline-flex items-center gap-1 rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm transition-all duration-200 hover:bg-gray-50 hover:shadow-md">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round" class="h-4 w-4">
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                            <polyline points="7 10 12 15 17 10"></polyline>
                            <line x1="12" y1="15" x2="12" y2="3"></line>
                        </svg>
                        Export Report
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round" class="h-4 w-4 transition-transform duration-200"
                            :class="{ 'rotate-180': open }">
                            <polyline points="6 9 12 15 18 9"></polyline>
                        </svg>
                    </button>
                    <div x-show="open" @click.away="open = false"
                        class="absolute right-0 mt-2 w-48 rounded-lg bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 z-10">
                        <a href="{{ route('seller.analytics.export.csv', ['range' => $timeRange ?? '30d']) }}"
                            class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="mr-2 h-4 w-4">
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                <polyline points="7 10 12 15 17 10"></polyline>
                                <line x1="12" y1="15" x2="12" y2="3"></line>
                            </svg>
                            <span>Export as CSV</span>
                        </a>
                        <a href="{{ route('seller.analytics.export.pdf', ['range' => $timeRange ?? '30d']) }}"
                            class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="mr-2 h-4 w-4">
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                <polyline points="7 10 12 15 17 10"></polyline>
                                <line x1="12" y1="15" x2="12" y2="3"></line>
                            </svg>
                            <span>Export as PDF</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="flex flex-wrap items-center gap-2 mb-2">
            <a href="{{ route('seller.analytics', ['range' => '7d'], false) ?? '#' }}"
                class="time-range-btn {{ ($timeRange ?? '30d') === '7d' ? 'active' : '' }}">Last 7 Days</a>
            <a href="{{ route('seller.analytics', ['range' => '30d'], false) ?? '#' }}"
                class="time-range-btn {{ ($timeRange ?? '30d') === '30d' ? 'active' : '' }}">Last 30 Days</a>
            <a href="{{ route('seller.analytics', ['range' => '90d'], false) ?? '#' }}"
                class="time-range-btn {{ ($timeRange ?? '30d') === '90d' ? 'active' : '' }}">Last 90 Days</a>
            <a href="{{ route('seller.analytics', ['range' => '1y'], false) ?? '#' }}"
                class="time-range-btn {{ ($timeRange ?? '30d') === '1y' ? 'active' : '' }}">Last Year</a>
        </div>

        <div class="grid gap-6 md:grid-cols-3">
            <div class="analytics-stat-card" style="--animation-order: 0">
                <div class="flex items-center justify-between pb-2">
                    <h3>Total Revenue (Successful Orders)</h3>
                    <div class="p-2 rounded-full {{ ($revenueChange ?? 0) >= 0 ? 'bg-green-50' : 'bg-red-50' }}">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round"
                            class="h-4 w-4 {{ ($revenueChange ?? 0) >= 0 ? 'text-green-500' : 'text-red-500' }}">
                            @if (($revenueChange ?? 0) >= 0)
                                <polyline points="23 6 13.5 15.5 8.5 10.5 1 18"></polyline>
                                <polyline points="17 6 23 6 23 12"></polyline>
                            @else
                                <polyline points="23 18 13.5 8.5 8.5 13.5 1 6"></polyline>
                                <polyline points="17 18 23 18 23 12"></polyline>
                            @endif
                        </svg>
                    </div>
                </div>
                <div class="value text-green-700">Rp {{ number_format($totalRevenue ?? 0, 0, ',', '.') }}</div>
                <p>
                    <span class="{{ ($revenueChange ?? 0) >= 0 ? 'text-green-500' : 'text-red-500' }} font-medium">
                        {{ ($revenueChange ?? 0) >= 0 ? '+' : '' }}{{ number_format($revenueChange ?? 0, 1) }}%
                    </span>
                    from previous period
                </p>
            </div>
            <div class="analytics-stat-card" style="--animation-order: 1">
                <div class="flex items-center justify-between pb-2">
                    <h3>Total Sales (Successful Orders)</h3>
                    <div class="p-2 rounded-full {{ ($salesChange ?? 0) >= 0 ? 'bg-green-50' : 'bg-red-50' }}">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round"
                            class="h-4 w-4 {{ ($salesChange ?? 0) >= 0 ? 'text-green-500' : 'text-red-500' }}">
                            @if (($salesChange ?? 0) >= 0)
                                <polyline points="23 6 13.5 15.5 8.5 10.5 1 18"></polyline>
                                <polyline points="17 6 23 6 23 12"></polyline>
                            @else
                                <polyline points="23 18 13.5 8.5 8.5 13.5 1 6"></polyline>
                                <polyline points="17 18 23 18 23 12"></polyline>
                            @endif
                        </svg>
                    </div>
                </div>
                <div class="value text-indigo-700">{{ $totalSales ?? 0 }}</div>
                <p>
                    <span class="{{ ($salesChange ?? 0) >= 0 ? 'text-green-500' : 'text-red-500' }} font-medium">
                        {{ ($salesChange ?? 0) >= 0 ? '+' : '' }}{{ number_format($salesChange ?? 0, 1) }}%
                    </span>
                    from previous period
                </p>
            </div>
            <div class="analytics-stat-card" style="--animation-order: 2">
                <div class="flex items-center justify-between pb-2">
                    <h3>Average Order Value (Successful Orders)</h3>
                    <div class="p-2 rounded-full bg-indigo-50">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round" class="h-4 w-4 text-indigo-600">
                            <circle cx="12" cy="12" r="10"></circle>
                            <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                        </svg>
                    </div>
                </div>
                <div class="value text-indigo-700">Rp {{ number_format($averageOrderValue ?? 0, 0, ',', '.') }}</div>
                <p>Per order average</p>
            </div>
        </div>

        <div class="grid gap-6 md:grid-cols-2">
            <div class="rounded-lg border bg-white shadow-sm chart-container">
                <div class="border-b p-6">
                    <h3 class="text-lg font-medium">Revenue Over Time (Successful Orders)</h3>
                    <p class="text-sm text-gray-500">Daily revenue from successful orders for the selected period</p>
                </div>
                <div class="p-6">
                    <div class="w-full rounded-lg bg-white p-4" style="height: 300px;">
                        <canvas id="revenueChart" width="100%" height="100%"></canvas>
                    </div>
                </div>
            </div>
            <div class="rounded-lg border bg-white shadow-sm chart-container">
                <div class="border-b p-6">
                    <h3 class="text-lg font-medium">Sales Over Time (Successful Orders)</h3>
                    <p class="text-sm text-gray-500">Daily successful sales count for the selected period</p>
                </div>
                <div class="p-6">
                    <div class="w-full rounded-lg bg-white p-4" style="height: 300px;">
                        <canvas id="salesChart" width="100%" height="100%"></canvas>
                    </div>
                </div>
            </div>
        </div>



        <div class="rounded-lg border bg-white shadow-sm chart-container">
            <div class="border-b p-6">
                <h3 class="text-lg font-medium">Top Selling Products (Successful Orders)</h3>
                <p class="text-sm text-gray-500">Your best performing products based on successful orders for the selected
                    period</p>
            </div>
            <div class="table-container overflow-x-auto">
                <table class="min-w-full">
                    <thead>
                        <tr>
                            <th>Product</th>
                            <th>Category</th>
                            <th class="text-right">Price</th>
                            <th class="text-right">Sales</th>
                            <th class="text-right">Revenue</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($topProducts ?? collect() as $product)
                            <tr class="hover:bg-gray-50 transition-colors">
                                <td class="font-medium">
                                    <div class="flex items-center">
                                        @if (method_exists($product, 'images') && $product->images()->where('is_primary', true)->first())
                                            <img src="{{ asset('storage/' . $product->images()->where('is_primary', true)->first()->path) }}"
                                                alt="{{ $product->name }}"
                                                class="w-10 h-10 rounded-md object-cover mr-3">
                                        @elseif(method_exists($product, 'images') && $product->images()->first())
                                            <img src="{{ asset('storage/' . $product->images()->first()->path) }}"
                                                alt="{{ $product->name }}"
                                                class="w-10 h-10 rounded-md object-cover mr-3">
                                        @else
                                            <div
                                                class="w-10 h-10 rounded-md bg-gray-200 flex items-center justify-center mr-3">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-400"
                                                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                        d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                </svg>
                                            </div>
                                        @endif
                                        {{ $product->name ?? 'N/A' }}
                                    </div>
                                </td>
                                <td>
                                    <span
                                        class="category-badge
                                @if ($product->category == 'digital') digital
                                @elseif($product->category == 'physical') physical
                                @elseif($product->category == 'service') service
                                @else other @endif">
                                        {{ ucfirst($product->category ?? 'Uncategorized') }}
                                    </span>
                                </td>
                                <td class="text-right">Rp {{ number_format($product->price ?? 0, 0, ',', '.') }}</td>
                                <td class="text-right">{{ $product->orders_count ?? 0 }}</td>
                                <td class="text-right font-medium">Rp
                                    {{ number_format($product->orders_sum_amount ?? 0, 0, ',', '.') }}</td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="5" class="text-center text-gray-500 py-8">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-gray-300 mb-3"
                                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1"
                                            d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
                                    </svg>
                                    <p>No sales data available for this period</p>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    @push('scripts')
        <!-- Debug info for chart data - commented out as requested -->
        {{-- <div class="mt-4 p-4 bg-gray-100 rounded-lg">
            <button id="toggle-debug" class="px-3 py-1 bg-gray-200 hover:bg-gray-300 rounded text-sm mb-2">Toggle Debug
                Info</button>
            <div id="debug-info" style="display: none;">
                <h3 class="font-bold mb-1">Revenue Data:</h3>
                <pre class="bg-white p-2 rounded overflow-auto max-h-40 text-xs">{{ json_encode($revenueData ?? [], JSON_PRETTY_PRINT) }}</pre>
                <h3 class="font-bold mt-3 mb-1">Sales Data:</h3>
                <pre class="bg-white p-2 rounded overflow-auto max-h-40 text-xs">{{ json_encode($salesData ?? [], JSON_PRETTY_PRINT) }}</pre>
            </div>
        </div> --}}

        <!-- Hidden data for charts -->
        <script id="revenue-data" type="application/json">
            @json($revenueData ?? [])
        </script>
        <script id="sales-data" type="application/json">
            @json($salesData ?? [])
        </script>

        <!-- External scripts -->
        <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
        <script src="{{ asset(js_path() . '/analytics.js') }}"></script>
    @endpush
@endsection
