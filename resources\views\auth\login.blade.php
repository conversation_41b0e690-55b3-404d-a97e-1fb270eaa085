<!DOCTYPE html>
<html lang="en">

<head>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-QWR2LGRD93"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-QWR2LGRD93');
    </script>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Log in to Digitora</title>
    <link rel="icon" type="image/x-icon" href="{{ asset('images/digitora-logo.png') }}">
    <link href="{{ asset('css/auth.css') }}" rel="stylesheet">
</head>

<body class="auth-page">
    <div class="auth-bg">
        <div class="container mx-auto px-4 py-4">
            <a href="{{ route('home') }}" class="back-link">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
                <span>Back to Home</span>
            </a>
        </div>

        <div class="flex-1 flex items-center justify-center px-4 py-8">
            <div class="auth-card">
                <div class="p-6 text-center">
                    <div class="logo-container">
                        <div class="logo">
                            <span>D</span>
                        </div>
                    </div>
                    <h2 class="text-2xl font-bold mb-2">Log in to Digitora</h2>
                    <p class="text-gray-600 text-sm">Enter your email and password to access your account</p>
                </div>

                <!-- Error Messages -->
                @if ($errors->any())
                    <div class="px-6">
                        <div class="alert alert-error">
                            @foreach ($errors->all() as $error)
                                <p>{{ $error }}</p>
                            @endforeach
                        </div>
                    </div>
                @endif

                <!-- Success Message -->
                @if (session('status'))
                    <div class="px-6">
                        <div class="alert alert-success">
                            <p>{{ session('status') }}</p>
                        </div>
                    </div>
                @endif

                <div class="p-6">
                    <form method="POST" action="{{ route('login') }}" class="auth-form">
                        @csrf
                        <div class="form-group">
                            <label for="email">Email</label>
                            <input id="email" name="email" type="email" placeholder="<EMAIL>"
                                value="{{ old('email') }}" required>
                        </div>
                        <div class="form-group">
                            <div class="flex items-center justify-between">
                                <label for="password">Password</label>
                                <a href="{{ route('password.request') }}"
                                    class="text-xs text-indigo-600 hover:underline">Forgot password?</a>
                            </div>
                            <input id="password" name="password" type="password" placeholder="••••••••" required>
                        </div>
                        <div class="checkbox-container">
                            <input type="checkbox" id="remember" name="remember">
                            <label for="remember" class="text-sm">Remember me</label>
                        </div>
                        <button type="submit" class="btn btn-primary w-full py-3">
                            Log in
                        </button>
                    </form>

                    <div class="divider">
                        <span>Or continue with</span>
                    </div>

                    <div class="w-full">
                        <a href="{{ route('auth.google') }}" class="social-btn w-full flex justify-center items-center">
                            <svg viewBox="0 0 24 24" class="w-5 h-5 mr-2">
                                <path
                                    d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                                    fill="#4285F4" />
                                <path
                                    d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                                    fill="#34A853" />
                                <path
                                    d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                                    fill="#FBBC05" />
                                <path
                                    d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                                    fill="#EA4335" />
                                <path d="M1 1h22v22H1z" fill="none" />
                            </svg>
                            Sign in with Google
                        </a>
                    </div>
                </div>

                <div class="auth-footer">
                    <p>
                        Don't have an account?
                        <a href="{{ route('register') }}">Sign up</a>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ asset(js_path() . '/auth.js') }}"></script>
</body>

</html>
