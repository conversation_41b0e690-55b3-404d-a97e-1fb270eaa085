<?php

namespace App\Http\Controllers\Seller;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\Product;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Response;

class AnalyticsController extends Controller
{
    public function index(Request $request)
    {
        $timeRange = $request->get('range', '30d');

        // Set date range based on selected time range
        $startDate = null;
        $endDate = Carbon::now();

        switch ($timeRange) {
            case '7d':
                $startDate = Carbon::now()->subDays(7);
                break;
            case '30d':
                $startDate = Carbon::now()->subDays(30);
                break;
            case '90d':
                $startDate = Carbon::now()->subDays(90);
                break;
            case '1y':
                $startDate = Carbon::now()->subYear();
                break;
            default:
                $startDate = Carbon::now()->subDays(30);
        }

        // Get revenue data for chart - only for successful orders
        $sellerId = Auth::id();
        $revenueData = DB::table('orders')
            ->join('products', 'orders.product_id', '=', 'products.id')
            ->select(
                DB::raw('DATE(orders.created_at) as date'),
                DB::raw('SUM(orders.amount) as revenue')
            )
            ->where('products.seller_id', '=', $sellerId)
            ->where('orders.status', '=', 'success')
            ->where('orders.payment_status', '=', 'paid')
            ->whereBetween('orders.created_at', [$startDate, $endDate])
            ->groupBy(DB::raw('DATE(orders.created_at)'))
            ->orderBy('date')
            ->get();

        // Get sales data for chart - only for successful orders
        $salesData = DB::table('orders')
            ->join('products', 'orders.product_id', '=', 'products.id')
            ->select(
                DB::raw('DATE(orders.created_at) as date'),
                DB::raw('COUNT(*) as count')
            )
            ->where('products.seller_id', '=', $sellerId)
            ->where('orders.status', '=', 'success')
            ->whereBetween('orders.created_at', [$startDate, $endDate])
            ->groupBy(DB::raw('DATE(orders.created_at)'))
            ->orderBy('date')
            ->get();

        // Get top products - only count successful orders
        $topProducts = Product::select('products.*')
            ->where('seller_id', $sellerId)
            ->withCount(['orders' => function ($query) use ($startDate, $endDate) {
                $query->where('status', '=', 'success')
                    ->whereBetween('created_at', [$startDate, $endDate]);
            }])
            ->withSum(['orders' => function ($query) use ($startDate, $endDate) {
                $query->where('status', '=', 'success')
                    ->whereBetween('created_at', [$startDate, $endDate]);
            }], 'amount')
            ->orderByDesc('orders_count')
            ->take(5)
            ->get();

        // Get product category distribution - only count successful orders
        $categoryData = DB::table('products')
            ->select(
                'category',
                DB::raw('COUNT(DISTINCT orders.id) as orders_count'),
                DB::raw('SUM(orders.amount) as orders_sum_amount')
            )
            ->leftJoin('orders', function ($join) use ($startDate, $endDate) {
                $join->on('products.id', '=', 'orders.product_id')
                    ->where('orders.status', '=', 'success')
                    ->whereBetween('orders.created_at', [$startDate, $endDate]);
            })
            ->where('products.seller_id', $sellerId)
            ->groupBy('category')
            ->orderByDesc('orders_count')
            ->get();

        // Get revenue by category over time - only for successful orders
        $categoryRevenueData = DB::table('orders')
            ->join('products', 'orders.product_id', '=', 'products.id')
            ->select(
                DB::raw('DATE(orders.created_at) as date'),
                'products.category',
                DB::raw('SUM(orders.amount) as revenue')
            )
            ->where('products.seller_id', '=', $sellerId)
            ->where('orders.status', '=', 'success')
            ->where('orders.payment_status', '=', 'paid')
            ->whereBetween('orders.created_at', [$startDate, $endDate])
            ->groupBy(DB::raw('DATE(orders.created_at)'), 'products.category')
            ->orderBy('date')
            ->get();

        // Get summary stats - only for successful orders
        $totalRevenue = Order::whereHas('product', function ($query) use ($sellerId) {
            $query->where('seller_id', '=', $sellerId);
        })
            ->where('status', '=', 'success')
            ->where('payment_status', '=', 'paid')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->sum('amount');

        $totalSales = Order::whereHas('product', function ($query) use ($sellerId) {
            $query->where('seller_id', '=', $sellerId);
        })
            ->where('status', '=', 'success')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        $averageOrderValue = $totalSales > 0 ? $totalRevenue / $totalSales : 0;

        // Get previous period data for comparison - only for successful orders
        $previousStartDate = (clone $startDate)->subDays($startDate->diffInDays($endDate));
        $previousEndDate = (clone $endDate)->subDays($startDate->diffInDays($endDate));

        $previousRevenue = Order::whereHas('product', function ($query) use ($sellerId) {
            $query->where('seller_id', '=', $sellerId);
        })
            ->where('status', '=', 'success')
            ->where('payment_status', '=', 'paid')
            ->whereBetween('created_at', [$previousStartDate, $previousEndDate])
            ->sum('amount');

        $previousSales = Order::whereHas('product', function ($query) use ($sellerId) {
            $query->where('seller_id', '=', $sellerId);
        })
            ->where('status', '=', 'success')
            ->whereBetween('created_at', [$previousStartDate, $previousEndDate])
            ->count();

        $revenueChange = $previousRevenue > 0 ? (($totalRevenue - $previousRevenue) / $previousRevenue) * 100 : ($totalRevenue > 0 ? 100 : 0);
        $salesChange = $previousSales > 0 ? (($totalSales - $previousSales) / $previousSales) * 100 : ($totalSales > 0 ? 100 : 0);

        // Ensure we have at least one data point for charts
        if ($revenueData->isEmpty()) {
            $revenueData = collect([
                ['date' => now()->format('Y-m-d'), 'revenue' => 0]
            ]);
        }

        if ($salesData->isEmpty()) {
            $salesData = collect([
                ['date' => now()->format('Y-m-d'), 'count' => 0]
            ]);
        }

        return view('seller.analytics.index', compact(
            'timeRange',
            'revenueData',
            'salesData',
            'topProducts',
            'totalRevenue',
            'totalSales',
            'averageOrderValue',
            'revenueChange',
            'salesChange',
            'categoryData',
            'categoryRevenueData'
        ));
    }

    /**
     * Export analytics data as CSV
     */
    public function exportCsv(Request $request)
    {
        $timeRange = $request->get('range', '30d');
        $data = $this->getAnalyticsData($timeRange);

        // Create CSV content
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="analytics_' . $timeRange . '_' . date('Y-m-d') . '.csv"',
            'Pragma' => 'no-cache',
            'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
            'Expires' => '0'
        ];

        $callback = function() use ($data) {
            $file = fopen('php://output', 'w');

            // Add headers
            fputcsv($file, ['Analytics Report', 'Period: ' . $data['timeRange'], 'Generated: ' . date('Y-m-d H:i:s')]);
            fputcsv($file, []);

            // Summary section
            fputcsv($file, ['Summary']);
            fputcsv($file, ['Total Revenue', 'Rp ' . number_format($data['totalRevenue'], 0, ',', '.')]);
            fputcsv($file, ['Total Sales', $data['totalSales']]);
            fputcsv($file, ['Average Order Value', 'Rp ' . number_format($data['averageOrderValue'], 0, ',', '.')]);
            fputcsv($file, []);

            // Revenue by date
            fputcsv($file, ['Revenue by Date']);
            fputcsv($file, ['Date', 'Revenue (Rp)']);
            foreach ($data['revenueData'] as $item) {
                fputcsv($file, [
                    Carbon::parse($item->date)->format('Y-m-d'),
                    number_format($item->revenue, 0, ',', '.')
                ]);
            }
            fputcsv($file, []);

            // Sales by date
            fputcsv($file, ['Sales by Date']);
            fputcsv($file, ['Date', 'Number of Sales']);
            foreach ($data['salesData'] as $item) {
                fputcsv($file, [
                    Carbon::parse($item->date)->format('Y-m-d'),
                    $item->count
                ]);
            }
            fputcsv($file, []);

            // Category data
            fputcsv($file, ['Category Analysis']);
            fputcsv($file, ['Category', 'Sales Count', 'Revenue (Rp)']);
            foreach ($data['categoryData'] as $item) {
                fputcsv($file, [
                    ucfirst($item->category ?? 'Uncategorized'),
                    $item->orders_count,
                    number_format($item->orders_sum_amount, 0, ',', '.')
                ]);
            }
            fputcsv($file, []);

            // Top products
            fputcsv($file, ['Top Products']);
            fputcsv($file, ['Product Name', 'Category', 'Price (Rp)', 'Sales Count', 'Revenue (Rp)']);
            foreach ($data['topProducts'] as $product) {
                fputcsv($file, [
                    $product->name,
                    ucfirst($product->category ?? 'Uncategorized'),
                    number_format($product->price, 0, ',', '.'),
                    $product->orders_count,
                    number_format($product->orders_sum_amount, 0, ',', '.')
                ]);
            }

            fclose($file);
        };

        return Response::stream($callback, 200, $headers);
    }

    /**
     * Export analytics data as PDF
     */
    public function exportPdf(Request $request)
    {
        $timeRange = $request->get('range', '30d');
        $data = $this->getAnalyticsData($timeRange);

        $pdf = PDF::loadView('seller.analytics.pdf', $data);

        return $pdf->download('analytics_' . $timeRange . '_' . date('Y-m-d') . '.pdf');
    }

    /**
     * Get analytics data for the given time range
     */
    private function getAnalyticsData($timeRange)
    {
        // Set date range based on selected time range
        $startDate = null;
        $endDate = Carbon::now();

        switch ($timeRange) {
            case '7d':
                $startDate = Carbon::now()->subDays(7);
                break;
            case '30d':
                $startDate = Carbon::now()->subDays(30);
                break;
            case '90d':
                $startDate = Carbon::now()->subDays(90);
                break;
            case '1y':
                $startDate = Carbon::now()->subYear();
                break;
            default:
                $startDate = Carbon::now()->subDays(30);
        }

        // Get revenue data for chart - only for successful orders
        $sellerId = Auth::id();
        $revenueData = DB::table('orders')
            ->join('products', 'orders.product_id', '=', 'products.id')
            ->select(
                DB::raw('DATE(orders.created_at) as date'),
                DB::raw('SUM(orders.amount) as revenue')
            )
            ->where('products.seller_id', '=', $sellerId)
            ->where('orders.status', '=', 'success')
            ->where('orders.payment_status', '=', 'paid')
            ->whereBetween('orders.created_at', [$startDate, $endDate])
            ->groupBy(DB::raw('DATE(orders.created_at)'))
            ->orderBy('date')
            ->get();

        // Get sales data for chart - only for successful orders
        $salesData = DB::table('orders')
            ->join('products', 'orders.product_id', '=', 'products.id')
            ->select(
                DB::raw('DATE(orders.created_at) as date'),
                DB::raw('COUNT(*) as count')
            )
            ->where('products.seller_id', '=', $sellerId)
            ->where('orders.status', '=', 'success')
            ->whereBetween('orders.created_at', [$startDate, $endDate])
            ->groupBy(DB::raw('DATE(orders.created_at)'))
            ->orderBy('date')
            ->get();

        // Get top products - only count successful orders
        $topProducts = Product::select('products.*')
            ->where('seller_id', $sellerId)
            ->withCount(['orders' => function ($query) use ($startDate, $endDate) {
                $query->where('status', '=', 'success')
                    ->whereBetween('created_at', [$startDate, $endDate]);
            }])
            ->withSum(['orders' => function ($query) use ($startDate, $endDate) {
                $query->where('status', '=', 'success')
                    ->whereBetween('created_at', [$startDate, $endDate]);
            }], 'amount')
            ->orderByDesc('orders_count')
            ->take(5)
            ->get();

        // Get product category distribution - only count successful orders
        $categoryData = DB::table('products')
            ->select(
                'category',
                DB::raw('COUNT(DISTINCT orders.id) as orders_count'),
                DB::raw('SUM(orders.amount) as orders_sum_amount')
            )
            ->leftJoin('orders', function ($join) use ($startDate, $endDate) {
                $join->on('products.id', '=', 'orders.product_id')
                    ->where('orders.status', '=', 'success')
                    ->whereBetween('orders.created_at', [$startDate, $endDate]);
            })
            ->where('products.seller_id', $sellerId)
            ->groupBy('category')
            ->orderByDesc('orders_count')
            ->get();

        // Get revenue by category over time - only for successful orders
        $categoryRevenueData = DB::table('orders')
            ->join('products', 'orders.product_id', '=', 'products.id')
            ->select(
                DB::raw('DATE(orders.created_at) as date'),
                'products.category',
                DB::raw('SUM(orders.amount) as revenue')
            )
            ->where('products.seller_id', '=', $sellerId)
            ->where('orders.status', '=', 'success')
            ->where('orders.payment_status', '=', 'paid')
            ->whereBetween('orders.created_at', [$startDate, $endDate])
            ->groupBy(DB::raw('DATE(orders.created_at)'), 'products.category')
            ->orderBy('date')
            ->get();

        // Get summary stats - only for successful orders
        $totalRevenue = Order::whereHas('product', function ($query) use ($sellerId) {
            $query->where('seller_id', '=', $sellerId);
        })
            ->where('status', '=', 'success')
            ->where('payment_status', '=', 'paid')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->sum('amount');

        $totalSales = Order::whereHas('product', function ($query) use ($sellerId) {
            $query->where('seller_id', '=', $sellerId);
        })
            ->where('status', '=', 'success')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        $averageOrderValue = $totalSales > 0 ? $totalRevenue / $totalSales : 0;

        // Get previous period data for comparison - only for successful orders
        $previousStartDate = (clone $startDate)->subDays($startDate->diffInDays($endDate));
        $previousEndDate = (clone $endDate)->subDays($startDate->diffInDays($endDate));

        $previousRevenue = Order::whereHas('product', function ($query) use ($sellerId) {
            $query->where('seller_id', '=', $sellerId);
        })
            ->where('status', '=', 'success')
            ->where('payment_status', '=', 'paid')
            ->whereBetween('created_at', [$previousStartDate, $previousEndDate])
            ->sum('amount');

        $previousSales = Order::whereHas('product', function ($query) use ($sellerId) {
            $query->where('seller_id', '=', $sellerId);
        })
            ->where('status', '=', 'success')
            ->whereBetween('created_at', [$previousStartDate, $previousEndDate])
            ->count();

        $revenueChange = $previousRevenue > 0 ? (($totalRevenue - $previousRevenue) / $previousRevenue) * 100 : ($totalRevenue > 0 ? 100 : 0);
        $salesChange = $previousSales > 0 ? (($totalSales - $previousSales) / $previousSales) * 100 : ($totalSales > 0 ? 100 : 0);

        // Ensure we have at least one data point for charts
        if ($revenueData->isEmpty()) {
            $revenueData = collect([
                (object)['date' => now()->format('Y-m-d'), 'revenue' => 0]
            ]);
        }

        if ($salesData->isEmpty()) {
            $salesData = collect([
                (object)['date' => now()->format('Y-m-d'), 'count' => 0]
            ]);
        }

        return [
            'timeRange' => $timeRange,
            'revenueData' => $revenueData,
            'salesData' => $salesData,
            'topProducts' => $topProducts,
            'totalRevenue' => $totalRevenue,
            'totalSales' => $totalSales,
            'averageOrderValue' => $averageOrderValue,
            'revenueChange' => $revenueChange,
            'salesChange' => $salesChange,
            'categoryData' => $categoryData,
            'categoryRevenueData' => $categoryRevenueData
        ];
    }
}
