// AI Chat Functionality
document.addEventListener('DOMContentLoaded', function() {
    // Chat state
    const chatState = {
        isOpen: false,
        conversation: null,
        messages: [],
        isAuthenticated: false,
        isTyping: false
    };

    // DOM Elements
    const chatContainer = document.querySelector('.ai-chat-container');
    const chatButton = document.querySelector('.ai-chat-button');
    const chatWindow = document.querySelector('.ai-chat-window');
    const chatMessages = document.querySelector('.ai-chat-messages');
    const chatInput = document.querySelector('.ai-chat-input');
    const chatSendButton = document.querySelector('.ai-chat-send-button');
    const chatClearButton = document.querySelector('.ai-chat-clear-button');
    const loginOverlay = document.querySelector('.ai-chat-login-overlay');
    const loginButton = document.querySelector('.ai-chat-login-button');

    // Initialize chat
    function initChat() {
        // Toggle chat window when button is clicked
        if (chatButton) {
            chatButton.addEventListener('click', toggleChat);
        }

        // Send message when send button is clicked or Enter key is pressed
        if (chatSendButton) {
            chatSendButton.addEventListener('click', sendMessage);
        }

        if (chatInput) {
            chatInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });
        }

        // Clear conversation when clear button is clicked
        if (chatClearButton) {
            chatClearButton.addEventListener('click', clearConversation);
        }

        // Redirect to login page when login button is clicked
        if (loginButton) {
            loginButton.addEventListener('click', function() {
                window.location.href = '/login?redirect=' + encodeURIComponent(window.location.pathname);
            });
        }

        // Get conversation on initial load
        getConversation();
    }

    // Toggle chat window
    function toggleChat() {
        chatState.isOpen = !chatState.isOpen;

        if (chatState.isOpen) {
            chatWindow.classList.add('active');
            // Scroll to bottom of messages
            scrollToBottom();
            // Focus input field
            if (chatInput) {
                chatInput.focus();
            }
        } else {
            chatWindow.classList.remove('active');
        }
    }

    // Get conversation from server
    function getConversation() {
        fetch('/ai-chat/conversation')
            .then(response => response.json())
            .then(data => {
                chatState.conversation = data.conversation;
                chatState.messages = data.messages;
                chatState.isAuthenticated = data.is_authenticated;

                // Show login overlay if user is not authenticated
                if (loginOverlay) {
                    if (!chatState.isAuthenticated) {
                        loginOverlay.style.display = 'flex';
                    } else {
                        loginOverlay.style.display = 'none';
                    }
                }

                // Render messages
                renderMessages();
            })
            .catch(error => {
                console.error('Error fetching conversation:', error);
            });
    }

    // Send message to server
    function sendMessage() {
        if (!chatInput || chatInput.value.trim() === '' || chatState.isTyping) {
            return;
        }

        const message = chatInput.value.trim();
        chatInput.value = '';

        // Add user message to UI immediately
        addMessage('user', message);

        // Show typing indicator
        showTypingIndicator();

        // Send message to server
        fetch('/ai-chat/send-message', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                conversation_id: chatState.conversation.id,
                message: message
            })
        })
        .then(response => response.json())
        .then(data => {
            // Hide typing indicator
            hideTypingIndicator();

            // Add AI response to UI
            addMessage('ai', data.ai_message.content);
        })
        .catch(error => {
            // Hide typing indicator
            hideTypingIndicator();

            console.error('Error sending message:', error);
            // Show error message
            addMessage('ai', 'Sorry, there was an error processing your request. Please try again later.');
        });
    }

    // Clear conversation
    function clearConversation() {
        if (!chatState.conversation || chatState.isTyping) {
            return;
        }

        fetch('/ai-chat/clear-conversation', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                conversation_id: chatState.conversation.id
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Clear messages in UI
                chatMessages.innerHTML = '';
                // Get fresh conversation
                getConversation();
            }
        })
        .catch(error => {
            console.error('Error clearing conversation:', error);
        });
    }

    // Add message to UI
    function addMessage(sender, content) {
        const messageElement = document.createElement('div');
        messageElement.classList.add('ai-chat-message', sender);
        messageElement.textContent = content;

        chatMessages.appendChild(messageElement);
        scrollToBottom();
    }

    // Render all messages
    function renderMessages() {
        if (!chatMessages) return;

        chatMessages.innerHTML = '';

        // Add all messages from state
        chatState.messages.forEach(message => {
            addMessage(message.sender_type, message.content);
        });
    }

    // Show typing indicator
    function showTypingIndicator() {
        chatState.isTyping = true;

        const typingIndicator = document.createElement('div');
        typingIndicator.classList.add('ai-chat-typing-indicator');
        typingIndicator.innerHTML = `
            <div class="ai-chat-typing-dot"></div>
            <div class="ai-chat-typing-dot"></div>
            <div class="ai-chat-typing-dot"></div>
        `;
        typingIndicator.id = 'typing-indicator';

        chatMessages.appendChild(typingIndicator);
        scrollToBottom();
    }

    // Hide typing indicator
    function hideTypingIndicator() {
        chatState.isTyping = false;

        const typingIndicator = document.getElementById('typing-indicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }

    // Scroll to bottom of messages
    function scrollToBottom() {
        if (chatMessages) {
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
    }

    // Initialize chat if container exists
    if (chatContainer) {
        initChat();
    }
});
