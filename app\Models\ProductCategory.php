<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductCategory extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'icon',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Get the subcategories for this category.
     */
    public function subcategories()
    {
        return $this->hasMany(ProductSubcategory::class, 'category_id')
            ->orderBy('sort_order')
            ->orderBy('name');
    }

    /**
     * Get active subcategories.
     */
    public function activeSubcategories()
    {
        return $this->hasMany(ProductSubcategory::class, 'category_id')
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->orderBy('name');
    }

    /**
     * Get products in this category.
     */
    public function products()
    {
        return $this->hasMany(Product::class, 'category_id');
    }

    /**
     * Scope a query to only include active categories.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get the legacy category code that corresponds to this category.
     */
    public function getLegacyCategoryAttribute()
    {
        $legacyMapping = [
            'productivity-tools' => null,
            'design-assets' => null,
            'development-resources' => null,
            'educational-content' => null,
        ];

        return $legacyMapping[$this->slug] ?? null;
    }
}
