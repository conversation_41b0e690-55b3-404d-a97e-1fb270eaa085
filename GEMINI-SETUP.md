# Setting Up Gemini AI Chat

This document provides instructions for setting up the Gemini AI Chat feature in your Digitora application.

## 1. Add Environment Variables

Add the following lines to your `.env` file:

```
GEMINI_API_KEY=AIzaSyBGd-8ZtsevUl4lr1Kqrg9WEMPC83F7gfQ
GEMINI_PROJECT_NUMBER=7712555130
GEMINI_MODEL=gemini-2.0-flash
```

## 2. Run Migrations

Run the following command to set up the necessary database tables:

```bash
php artisan setup:ai-chat
```

This command will:
- Create the `ai_conversations` table
- Create the `ai_messages` table
- Remind you about the environment variables

## 3. Testing the Chat

Once the setup is complete, you should see a chat button in the bottom-right corner of every page on your website. Click on it to open the chat window.

- If you're not logged in, you'll be prompted to log in first
- If you're logged in, you can start chatting with the AI assistant immediately

## Customization

You can customize the AI behavior by modifying the following files:

- `app/Services/GeminiService.php` - Adjust the API parameters or add more functionality
- `app/Http/Controllers/AiChatController.php` - Modify how conversations are handled
- `resources/views/components/ai-chat.blade.php` - Change the chat UI
- `public/css/ai-chat.css` - Customize the chat appearance
- `public/dev-js/ai-chat.js` - Modify the chat behavior

## Troubleshooting

If you encounter any issues:

1. Check that the Gemini API key is correctly set in your `.env` file
2. Ensure the migrations have run successfully
3. Check the Laravel logs for any errors
4. Make sure JavaScript is enabled in your browser
5. Clear your browser cache if you've made changes to the CSS or JavaScript files

For more information about the Gemini API, visit: https://ai.google.dev/docs/gemini_api
